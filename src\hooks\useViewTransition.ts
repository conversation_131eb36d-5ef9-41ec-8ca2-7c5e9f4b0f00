import { useState, useEffect, useRef } from 'react';

export type ViewType = 'home' | 'detail' | 'chat' | 'unavailable';

interface ViewTransitionState {
  currentView: ViewType;
  previousView: ViewType | null;
  isTransitioning: boolean;
  transitionDirection: 'forward' | 'backward' | 'none';
}

export const useViewTransition = (initialView: ViewType = 'home') => {
  const [state, setState] = useState<ViewTransitionState>({
    currentView: initialView,
    previousView: null,
    isTransitioning: false,
    transitionDirection: 'none'
  });

  const transitionTimeoutRef = useRef<NodeJS.Timeout>();

  // Definir la jerarquía de vistas para determinar la dirección
  const viewHierarchy: Record<ViewType, number> = {
    home: 0,
    detail: 1,
    chat: 2,
    unavailable: 1
  };

  const changeView = (newView: ViewType, duration: number = 300) => {
    if (state.currentView === newView || state.isTransitioning) {
      return;
    }

    const currentLevel = viewHierarchy[state.currentView];
    const newLevel = viewHierarchy[newView];
    const direction = newLevel > currentLevel ? 'forward' : 'backward';

    setState(prev => ({
      ...prev,
      previousView: prev.currentView,
      isTransitioning: true,
      transitionDirection: direction
    }));

    // Limpiar timeout anterior si existe
    if (transitionTimeoutRef.current) {
      clearTimeout(transitionTimeoutRef.current);
    }

    // Cambiar la vista después de la duración de la transición
    transitionTimeoutRef.current = setTimeout(() => {
      setState(prev => ({
        ...prev,
        currentView: newView,
        isTransitioning: false,
        transitionDirection: 'none'
      }));
    }, duration);
  };

  const getTransitionType = (view: ViewType, isEntering: boolean): string => {
    if (state.transitionDirection === 'none') {
      return 'fade';
    }

    const isForward = state.transitionDirection === 'forward';
    
    if (isEntering) {
      return isForward ? 'slide-right' : 'slide-left';
    } else {
      return isForward ? 'slide-left' : 'slide-right';
    }
  };

  // Cleanup en unmount
  useEffect(() => {
    return () => {
      if (transitionTimeoutRef.current) {
        clearTimeout(transitionTimeoutRef.current);
      }
    };
  }, []);

  return {
    currentView: state.currentView,
    previousView: state.previousView,
    isTransitioning: state.isTransitioning,
    transitionDirection: state.transitionDirection,
    changeView,
    getTransitionType
  };
};
