import React, { useState } from 'react';
import { X, User, Phone, Mail, Calendar, DollarSign } from 'lucide-react';

interface ContactData {
  dni: string;
  nombre: string;
  apellido: string;
  phone1: string;
  fec_vencimiento: string;
  fec_plazo: string;
  monto_deuda: string;
  propuesta_deuda: string;
  correo_electronico: string;
}

interface ContactFormModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: ContactData) => void;
}

const ContactFormModal: React.FC<ContactFormModalProps> = ({ isOpen, onClose, onSubmit }) => {
  const [formData, setFormData] = useState<ContactData>({
    dni: '',
    nombre: '',
    apellido: '',
    phone1: '',
    fec_vencimiento: '',
    fec_plazo: '',
    monto_deuda: '',
    propuesta_deuda: '',
    correo_electronico: ''
  });

  const [errors, setErrors] = useState<Partial<ContactData>>({});

  const handleInputChange = (field: keyof ContactData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Limpiar error cuando el usuario empiece a escribir
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Partial<ContactData> = {};

    if (!formData.dni.trim()) newErrors.dni = 'DNI es requerido';
    if (!formData.nombre.trim()) newErrors.nombre = 'Nombre es requerido';
    if (!formData.apellido.trim()) newErrors.apellido = 'Apellido es requerido';
    if (!formData.phone1.trim()) newErrors.phone1 = 'Teléfono es requerido';
    if (!formData.fec_vencimiento) newErrors.fec_vencimiento = 'Fecha de vencimiento es requerida';
    if (!formData.fec_plazo) newErrors.fec_plazo = 'Fecha de plazo es requerida';
    if (!formData.monto_deuda.trim()) newErrors.monto_deuda = 'Monto de deuda es requerido';
    if (!formData.propuesta_deuda.trim()) newErrors.propuesta_deuda = 'Propuesta de deuda es requerida';
    if (!formData.correo_electronico.trim()) {
      newErrors.correo_electronico = 'Correo electrónico es requerido';
    } else if (!/\S+@\S+\.\S+/.test(formData.correo_electronico)) {
      newErrors.correo_electronico = 'Correo electrónico no válido';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (validateForm()) {
      onSubmit(formData);
      // Resetear formulario
      setFormData({
        dni: '',
        nombre: '',
        apellido: '',
        phone1: '',
        fec_vencimiento: '',
        fec_plazo: '',
        monto_deuda: '',
        propuesta_deuda: '',
        correo_electronico: ''
      });
      setErrors({});
      onClose();
    }
  };

  const handleClose = () => {
    setFormData({
      dni: '',
      nombre: '',
      apellido: '',
      phone1: '',
      fec_vencimiento: '',
      fec_plazo: '',
      monto_deuda: '',
      propuesta_deuda: '',
      correo_electronico: ''
    });
    setErrors({});
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      {/* Backdrop */}
      <div className="fixed inset-0 bg-black/50 backdrop-blur-sm transition-opacity" onClick={handleClose}></div>
      
      {/* Modal */}
      <div className="flex min-h-full items-center justify-center p-4">
        <div className="relative w-full max-w-2xl bg-white rounded-2xl shadow-2xl transform transition-all">
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-gray-200">
            <div className="flex items-center">
              <div className="h-10 w-10 bg-blue-100 rounded-xl flex items-center justify-center mr-3">
                <User className="h-5 w-5 text-blue-600" />
              </div>
              <div>
                <h2 className="text-xl font-bold text-gray-900">Cargar Contacto</h2>
                <p className="text-sm text-gray-500">Ingresa los datos del contacto para cobranza</p>
              </div>
            </div>
            <button
              onClick={handleClose}
              className="p-2 rounded-lg text-gray-400 hover:text-gray-600 hover:bg-gray-100 transition-colors"
            >
              <X className="h-5 w-5" />
            </button>
          </div>

          {/* Form */}
          <form onSubmit={handleSubmit} className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* DNI */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  DNI *
                </label>
                <input
                  type="text"
                  value={formData.dni}
                  onChange={(e) => handleInputChange('dni', e.target.value)}
                  className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                    errors.dni ? 'border-red-500' : 'border-gray-300'
                  }`}
                  placeholder="Ingresa el DNI"
                />
                {errors.dni && <p className="text-red-500 text-xs mt-1">{errors.dni}</p>}
              </div>

              {/* Nombre */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Nombre *
                </label>
                <input
                  type="text"
                  value={formData.nombre}
                  onChange={(e) => handleInputChange('nombre', e.target.value)}
                  className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                    errors.nombre ? 'border-red-500' : 'border-gray-300'
                  }`}
                  placeholder="Ingresa el nombre"
                />
                {errors.nombre && <p className="text-red-500 text-xs mt-1">{errors.nombre}</p>}
              </div>

              {/* Apellido */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Apellido *
                </label>
                <input
                  type="text"
                  value={formData.apellido}
                  onChange={(e) => handleInputChange('apellido', e.target.value)}
                  className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                    errors.apellido ? 'border-red-500' : 'border-gray-300'
                  }`}
                  placeholder="Ingresa el apellido"
                />
                {errors.apellido && <p className="text-red-500 text-xs mt-1">{errors.apellido}</p>}
              </div>

              {/* Teléfono */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  <Phone className="h-4 w-4 inline mr-1" />
                  Teléfono *
                </label>
                <input
                  type="tel"
                  value={formData.phone1}
                  onChange={(e) => handleInputChange('phone1', e.target.value)}
                  className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                    errors.phone1 ? 'border-red-500' : 'border-gray-300'
                  }`}
                  placeholder="+56 9 1234 5678"
                />
                {errors.phone1 && <p className="text-red-500 text-xs mt-1">{errors.phone1}</p>}
              </div>

              {/* Fecha Vencimiento */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  <Calendar className="h-4 w-4 inline mr-1" />
                  Fecha Vencimiento *
                </label>
                <input
                  type="date"
                  value={formData.fec_vencimiento}
                  onChange={(e) => handleInputChange('fec_vencimiento', e.target.value)}
                  className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                    errors.fec_vencimiento ? 'border-red-500' : 'border-gray-300'
                  }`}
                />
                {errors.fec_vencimiento && <p className="text-red-500 text-xs mt-1">{errors.fec_vencimiento}</p>}
              </div>

              {/* Fecha Plazo */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  <Calendar className="h-4 w-4 inline mr-1" />
                  Fecha Plazo *
                </label>
                <input
                  type="date"
                  value={formData.fec_plazo}
                  onChange={(e) => handleInputChange('fec_plazo', e.target.value)}
                  className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                    errors.fec_plazo ? 'border-red-500' : 'border-gray-300'
                  }`}
                />
                {errors.fec_plazo && <p className="text-red-500 text-xs mt-1">{errors.fec_plazo}</p>}
              </div>

              {/* Monto Deuda */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  <DollarSign className="h-4 w-4 inline mr-1" />
                  Monto Deuda *
                </label>
                <input
                  type="number"
                  step="0.01"
                  value={formData.monto_deuda}
                  onChange={(e) => handleInputChange('monto_deuda', e.target.value)}
                  className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                    errors.monto_deuda ? 'border-red-500' : 'border-gray-300'
                  }`}
                  placeholder="0.00"
                />
                {errors.monto_deuda && <p className="text-red-500 text-xs mt-1">{errors.monto_deuda}</p>}
              </div>

              {/* Propuesta Deuda */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  <DollarSign className="h-4 w-4 inline mr-1" />
                  Propuesta Deuda *
                </label>
                <input
                  type="number"
                  step="0.01"
                  value={formData.propuesta_deuda}
                  onChange={(e) => handleInputChange('propuesta_deuda', e.target.value)}
                  className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                    errors.propuesta_deuda ? 'border-red-500' : 'border-gray-300'
                  }`}
                  placeholder="0.00"
                />
                {errors.propuesta_deuda && <p className="text-red-500 text-xs mt-1">{errors.propuesta_deuda}</p>}
              </div>

              {/* Correo Electrónico */}
              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  <Mail className="h-4 w-4 inline mr-1" />
                  Correo Electrónico *
                </label>
                <input
                  type="email"
                  value={formData.correo_electronico}
                  onChange={(e) => handleInputChange('correo_electronico', e.target.value)}
                  className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                    errors.correo_electronico ? 'border-red-500' : 'border-gray-300'
                  }`}
                  placeholder="<EMAIL>"
                />
                {errors.correo_electronico && <p className="text-red-500 text-xs mt-1">{errors.correo_electronico}</p>}
              </div>
            </div>

            {/* Buttons */}
            <div className="flex justify-end space-x-3 mt-8 pt-6 border-t border-gray-200">
              <button
                type="button"
                onClick={handleClose}
                className="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
              >
                Cancelar
              </button>
              <button
                type="submit"
                className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                Guardar Contacto
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default ContactFormModal;
