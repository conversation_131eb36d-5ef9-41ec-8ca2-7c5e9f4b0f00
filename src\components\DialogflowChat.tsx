import React, { useEffect, useRef, useState } from 'react';
import { Bot } from '../types/bot';
import { ArrowLeft, Minimize2, MessageCircle, Loader2, Sparkles, Maximize2, X } from 'lucide-react';
import { getDialogflowConfig } from '../config/dialogflow';
import { useErrorLogger } from '../utils/errorLogger';
import { useConnectionHealth } from '../hooks/useNetworkStatus';

interface DialogflowChatProps {
  bot: Bot;
  onBack: () => void;
  isWidget?: boolean;
  onShowUnavailable?: () => void;
}

// Tipos de errores específicos para mejor manejo
type ErrorType =
  | 'CONFIG_NOT_FOUND'
  | 'SCRIPT_LOAD_FAILED'
  | 'NETWORK_ERROR'
  | 'WIDGET_INIT_FAILED'
  | 'TIMEOUT_ERROR'
  | 'UNKNOWN_ERROR';

// Estado global para el script de Dialogflow
let dialogflowScriptLoaded = false;
let dialogflowScriptLoading = false;
let dialogflowLoadPromise: Promise<void> | null = null;

// Función para cargar el script de Dialogflow de manera singleton
const loadDialogflowScript = (): Promise<void> => {
  // Si ya está cargado, resolver inmediatamente
  if (dialogflowScriptLoaded) {
    return Promise.resolve();
  }

  // Si ya se está cargando, devolver la promesa existente
  if (dialogflowScriptLoading && dialogflowLoadPromise) {
    return dialogflowLoadPromise;
  }

  // Verificar si el script ya existe en el DOM
  const existingScript = document.querySelector('script[src*="df-messenger.js"]');
  if (existingScript && typeof customElements !== 'undefined' && customElements.get('df-messenger')) {
    dialogflowScriptLoaded = true;
    return Promise.resolve();
  }

  dialogflowScriptLoading = true;
  dialogflowLoadPromise = new Promise((resolve, reject) => {
    const script = document.createElement('script');
    script.src = 'https://www.gstatic.com/dialogflow-console/fast/df-messenger/prod/v1/df-messenger.js';
    script.async = true;

    const timeout = setTimeout(() => {
      dialogflowScriptLoading = false;
      dialogflowLoadPromise = null;
      reject(new Error('Timeout loading Dialogflow script'));
    }, 15000);

    script.onload = () => {
      clearTimeout(timeout);
      dialogflowScriptLoaded = true;
      dialogflowScriptLoading = false;
      resolve();
    };

    script.onerror = (error) => {
      clearTimeout(timeout);
      dialogflowScriptLoading = false;
      dialogflowLoadPromise = null;
      reject(error);
    };

    document.head.appendChild(script);
  });

  return dialogflowLoadPromise;
};

interface ErrorState {
  hasError: boolean;
  type: ErrorType | null;
  message: string;
  retryCount: number;
}

const DialogflowChat: React.FC<DialogflowChatProps> = ({ bot, onBack, isWidget = false, onShowUnavailable }) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [loadingMessage, setLoadingMessage] = useState('Inicializando chat...');
  const [isMaximized, setIsMaximized] = useState(false);
  const [error, setError] = useState<ErrorState>({
    hasError: false,
    type: null,
    message: '',
    retryCount: 0
  });
  const [retryAttempt, setRetryAttempt] = useState(0);
  
  const { logError, logWarn, logInfo } = useErrorLogger();
  const { isHealthy, hasIssues, connectionMessage } = useConnectionHealth();
  
  const MAX_RETRIES = 3;
  const SCRIPT_LOAD_TIMEOUT = 10000; // 10 segundos
  const WIDGET_INIT_TIMEOUT = 5000;  // 5 segundos

  // Función para manejar errores de manera consistente
  const handleError = (type: ErrorType, message: string, originalError?: any) => {
    logError('DialogflowChat', message, originalError, {
      botId: bot.id,
      botName: bot.name,
      errorType: type,
      retryAttempt,
      isWidget,
      connectionStatus: connectionMessage
    });

    // Si es un widget y hay errores críticos, redirigir a pantalla de no disponible
    if (isWidget && (type === 'CONFIG_NOT_FOUND' || type === 'SCRIPT_LOAD_FAILED') && onShowUnavailable) {
      onShowUnavailable();
      return;
    }

    setError({
      hasError: true,
      type,
      message,
      retryCount: retryAttempt
    });
    setIsLoading(false);
    setLoadingMessage('');
  };

  // Función para reiniciar el estado de error
  const resetError = () => {
    setError({
      hasError: false,
      type: null,
      message: '',
      retryCount: 0
    });
  };

  // Función para reintentar la carga
  const handleRetry = () => {
    if (retryAttempt < MAX_RETRIES) {
      logInfo('DialogflowChat', `Reintentando carga del widget (intento ${retryAttempt + 2})`, {
        botId: bot.id,
        previousError: error.type,
        connectionStatus: connectionMessage
      });
      
      setRetryAttempt(prev => prev + 1);
      resetError();
      setIsLoading(true);
      setLoadingMessage('Reintentando conexión...');
    }
  };

  useEffect(() => {
    let mounted = true;

    // Función para limpiar widgets anteriores - simplificada
    const cleanupPreviousWidgets = () => {
      // Solo limpiar contenedor si no es widget
      if (!isWidget && containerRef.current) {
        containerRef.current.innerHTML = '';
      }
      // No tocar widgets existentes para evitar conflictos
    };

    const loadDialogflow = async () => {
      const startTime = Date.now();
      try {
        setLoadingMessage('Verificando configuración...');

        // Limpiar widgets anteriores ANTES de crear uno nuevo
        cleanupPreviousWidgets();

        // Obtener la configuración del bot desde el mapa
        const botConfig = getDialogflowConfig(bot.id);
        if (!botConfig) {
          handleError(
            'CONFIG_NOT_FOUND',
            `No se encontró configuración de Dialogflow para el bot: ${bot.id}`
          );
          return;
        }

        // Validar que la configuración tenga todos los campos requeridos
        if (!botConfig.projectId || !botConfig.agentId) {
          handleError(
            'CONFIG_NOT_FOUND',
            `Configuración incompleta para el bot ${bot.name}. Faltan projectId o agentId.`
          );
          return;
        }

        setLoadingMessage('Cargando recursos de Dialogflow...');

        // Verificar conectividad de red básica
        if (!navigator.onLine) {
          handleError(
            'NETWORK_ERROR',
            'No hay conexión a internet. Verifica tu conexión y vuelve a intentar.'
          );
          return;
        }

        // Advertir sobre conexiones lentas
        if (hasIssues) {
          logWarn('DialogflowChat', `Conexión con problemas detectada: ${connectionMessage}`, {
            botId: bot.id,
            connectionStatus: connectionMessage
          });
          setLoadingMessage('Conexión lenta detectada, esto puede tomar más tiempo...');
        }

        // Load Dialogflow default CSS theme con manejo de errores (solo si no existe)
        let cssLink = document.querySelector('link[href*="df-messenger-default.css"]') as HTMLLinkElement;
        if (!cssLink) {
          cssLink = document.createElement('link');
          cssLink.rel = 'stylesheet';
          cssLink.href = 'https://www.gstatic.com/dialogflow-console/fast/df-messenger/prod/v1/themes/df-messenger-default.css';

          cssLink.onerror = () => {
            console.warn('[DialogflowChat] Failed to load CSS, continuing without default theme');
          };

          document.head.appendChild(cssLink);
        }

        setLoadingMessage('Cargando Dialogflow...');

        // Usar el sistema singleton para cargar el script
        try {
          await loadDialogflowScript();

          if (!mounted) return;

          setLoadingMessage('Configurando interfaz de chat...');

          // Verificar que df-messenger esté disponible
          if (typeof customElements === 'undefined' || !customElements.get('df-messenger')) {
            handleError(
              'WIDGET_INIT_FAILED',
              'El componente df-messenger no se cargó correctamente.'
            );
            return;
          }

          // Inject modern, colorful and minimalist CSS styles
          const style = document.createElement('style');
          style.textContent = `
            df-messenger {
              /* Modern gradient background */
              --df-messenger-chat-background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
              --df-messenger-chat-background-color: #ffffff;

              /* Beautiful borders and shadows */
              --df-messenger-chat-border: none;
              --df-messenger-chat-border-radius: 24px;
              --df-messenger-chat-box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(255, 255, 255, 0.1);

              /* Modern color palette */
              --df-messenger-primary-color: #667eea;
              --df-messenger-secondary-color: #764ba2;

              /* User messages - gradient bubble */
              --df-messenger-user-message: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
              --df-messenger-user-message-background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
              --df-messenger-user-message-font-color: #ffffff;

              /* Bot messages - clean white with subtle shadow */
              --df-messenger-bot-message: #ffffff;
              --df-messenger-bot-message-background: #ffffff;
              --df-messenger-bot-message-font-color: #374151;
              --df-messenger-bot-message-border: 1px solid #f1f5f9;
              --df-messenger-bot-message-box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);

              /* Titlebar - gradient header */
              --df-messenger-button-titlebar-color: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
              --df-messenger-button-titlebar-font-color: #ffffff;
              --df-messenger-titlebar-background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
              --df-messenger-titlebar-font-color: #ffffff;

              /* Input area - modern glass effect */
              --df-messenger-input-box-color: rgba(255, 255, 255, 0.95);
              --df-messenger-input-background: rgba(255, 255, 255, 0.95);
              --df-messenger-input-font-color: #374151;
              --df-messenger-input-placeholder-font-color: #9CA3AF;
              --df-messenger-input-border: 1px solid rgba(102, 126, 234, 0.2);
              --df-messenger-input-border-radius: 16px;
              --df-messenger-input-box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);

              /* Send button - gradient */
              --df-messenger-send-icon: #ffffff;
              --df-messenger-send-icon-color: #ffffff;
              --df-messenger-send-icon-background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);

              /* Close and minimize buttons */
              --df-messenger-minimized-chat-close-icon-color: #ffffff;
              --df-messenger-close-icon-color: #ffffff;

              /* Typography */
              --df-messenger-font-color: #374151;
              --df-messenger-font-family: 'Inter', 'Segoe UI', system-ui, sans-serif;
              --df-messenger-font-size: 14px;

              /* Animations */
              --df-messenger-animation-duration: 0.3s;
              --df-messenger-animation-easing: cubic-bezier(0.4, 0, 0.2, 1);
            }

            /* Additional custom styles for modern look */
            df-messenger::part(chat-wrapper) {
              backdrop-filter: blur(20px);
              -webkit-backdrop-filter: blur(20px);
            }

            df-messenger::part(titlebar) {
              background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
              border-radius: 24px 24px 0 0 !important;
              padding: 16px 20px !important;
              box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1) !important;
            }

            df-messenger::part(chat-bubble) {
              background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
              box-shadow: 0 10px 25px -5px rgba(102, 126, 234, 0.4) !important;
              border: none !important;
              transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
              animation: bubbleSlideIn 0.5s cubic-bezier(0.4, 0, 0.2, 1) !important;
            }

            df-messenger::part(chat-bubble):hover {
              transform: translateY(-2px) scale(1.02) !important;
              box-shadow: 0 20px 40px -10px rgba(102, 126, 234, 0.5) !important;
            }

            @keyframes bubbleSlideIn {
              from {
                transform: translateY(20px) scale(0.95);
                opacity: 0;
              }
              to {
                transform: translateY(0) scale(1);
                opacity: 1;
              }
            }

            /* Maximized mode styles with animations */
            .df-messenger-maximized {
              position: fixed !important;
              top: 0 !important;
              left: 0 !important;
              width: 100vw !important;
              height: 100vh !important;
              z-index: 9999 !important;
              border-radius: 0 !important;
              box-shadow: none !important;
              animation: maximizeIn 0.4s cubic-bezier(0.4, 0, 0.2, 1) forwards !important;
            }

            .df-messenger-maximized df-messenger {
              width: 100% !important;
              height: 100% !important;
              border-radius: 0 !important;
              box-shadow: none !important;
              transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
            }

            .df-messenger-maximized::part(titlebar) {
              border-radius: 0 !important;
              padding: 20px 24px !important;
              background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
            }

            @keyframes maximizeIn {
              from {
                transform: scale(0.8);
                opacity: 0;
              }
              to {
                transform: scale(1);
                opacity: 1;
              }
            }

            /* Maximize overlay with enhanced animations */
            .df-messenger-overlay {
              position: fixed;
              top: 0;
              left: 0;
              width: 100vw;
              height: 100vh;
              background: rgba(0, 0, 0, 0.8);
              backdrop-filter: blur(10px);
              -webkit-backdrop-filter: blur(10px);
              z-index: 9998;
              display: flex;
              align-items: center;
              justify-content: center;
              animation: overlayFadeIn 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            }

            .df-messenger-overlay.closing {
              animation: overlayFadeOut 0.3s cubic-bezier(0.4, 0, 0.2, 1) forwards;
            }

            @keyframes overlayFadeIn {
              from {
                opacity: 0;
                backdrop-filter: blur(0px);
                -webkit-backdrop-filter: blur(0px);
              }
              to {
                opacity: 1;
                backdrop-filter: blur(10px);
                -webkit-backdrop-filter: blur(10px);
              }
            }

            @keyframes overlayFadeOut {
              from {
                opacity: 1;
                backdrop-filter: blur(10px);
                -webkit-backdrop-filter: blur(10px);
              }
              to {
                opacity: 0;
                backdrop-filter: blur(0px);
                -webkit-backdrop-filter: blur(0px);
              }
            }

            /* Custom maximize button - mobile optimized */
            .df-messenger-controls {
              position: absolute;
              top: 12px;
              right: 12px;
              z-index: 10000;
              display: flex;
              gap: 6px;
            }

            .df-messenger-control-btn {
              width: 36px;
              height: 36px;
              border-radius: 10px;
              background: rgba(255, 255, 255, 0.2);
              backdrop-filter: blur(10px);
              -webkit-backdrop-filter: blur(10px);
              border: 1px solid rgba(255, 255, 255, 0.3);
              color: white;
              display: flex;
              align-items: center;
              justify-content: center;
              cursor: pointer;
              transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
              touch-action: manipulation;
              -webkit-tap-highlight-color: transparent;
            }

            .df-messenger-control-btn:hover {
              background: rgba(255, 255, 255, 0.3);
              transform: scale(1.05);
            }

            .df-messenger-control-btn:active {
              transform: scale(0.95);
              background: rgba(255, 255, 255, 0.4);
            }

            /* Mobile specific styles */
            @media (max-width: 640px) {
              .df-messenger-controls {
                top: 8px;
                right: 8px;
                gap: 4px;
              }

              .df-messenger-control-btn {
                width: 40px;
                height: 40px;
                border-radius: 12px;
              }

              .df-messenger-maximized::part(titlebar) {
                padding: 16px 20px !important;
              }

              /* Ensure full screen on mobile */
              .df-messenger-maximized {
                top: 0 !important;
                left: 0 !important;
                width: 100vw !important;
                height: 100vh !important;
                height: 100dvh !important; /* Dynamic viewport height */
              }

              .df-messenger-overlay {
                height: 100vh !important;
                height: 100dvh !important; /* Dynamic viewport height */
              }
            }
          `;
          document.head.appendChild(style);

          setLoadingMessage('Inicializando widget de chat...');

          // Create messenger element
          const messenger = document.createElement('df-messenger');
          
          // Configurar atributos con validación
          messenger.setAttribute('project-id', botConfig.projectId);
          messenger.setAttribute('agent-id', botConfig.agentId);
          messenger.setAttribute('language-code', 'es');
          messenger.setAttribute('max-query-length', '-1');
          messenger.setAttribute('type', isWidget ? 'floating' : 'inline');
          messenger.setAttribute('chat-title', botConfig.chatTitle);

          // Optional: add a box shadow for better separation
          messenger.style.boxShadow = '0 4px 6px rgba(0,0,0,0.1)';
          messenger.style.borderRadius = '20px';

          // Minimal event listeners to avoid interference
          messenger.addEventListener('df-messenger-error', (event: any) => {
            console.error('[DialogflowChat] Widget error:', event.detail);
            // Don't handle errors aggressively to avoid closing the widget
          });

          if (isWidget) {
            document.body.appendChild(messenger);
            console.log('[DialogflowChat] Widget added to body, letting it work naturally');
            
          } else if (containerRef.current) {
            containerRef.current.innerHTML = '';
            containerRef.current.appendChild(messenger);
          } else {
            handleError(
              'WIDGET_INIT_FAILED',
              'No se pudo encontrar el contenedor para el widget de chat'
            );
            return;
          }

          // Timeout para la inicialización del widget
          const initTimeout = setTimeout(() => {
            if (mounted) {
              handleError(
                'TIMEOUT_ERROR',
                'El widget de Dialogflow tardó demasiado en inicializarse'
              );
            }
          }, WIDGET_INIT_TIMEOUT);

          // Simple verification that widget was added
          setTimeout(() => {
            if (mounted) {
              clearTimeout(initTimeout);
              logInfo('DialogflowChat', 'Widget de Dialogflow inicializado correctamente', {
                botId: bot.id,
                botName: bot.name,
                isWidget,
                retryAttempt,
                loadTime: Date.now() - startTime
              });
              setLoadingMessage('');
              setIsLoading(false);
            }
          }, 1500);
        } catch (scriptError) {
          if (mounted) {
            handleError(
              'SCRIPT_LOAD_FAILED',
              'No se pudo cargar el script de Dialogflow. Verifica tu conexión a internet.',
              scriptError
            );
          }
        }

        return () => {
          mounted = false;
          // Minimal cleanup to avoid interfering with widget
          console.log('[DialogflowChat] Component unmounting, minimal cleanup');
        };
      } catch (generalError) {
        if (mounted) {
          handleError(
            'UNKNOWN_ERROR',
            'Error inesperado al cargar Dialogflow',
            generalError
          );
        }
      }
    };

    loadDialogflow();
    return () => {
      mounted = false;
      cleanupPreviousWidgets();
    };
  }, [bot.id, isWidget, onBack, retryAttempt]);

  // Add custom controls after widget is loaded
  useEffect(() => {
    if (isWidget && !isLoading) {
      const addControls = () => {
        const messengerElement = document.querySelector('df-messenger');
        if (messengerElement && !messengerElement.querySelector('.df-messenger-controls')) {
          // Create floating controls
          const controls = document.createElement('div');
          controls.className = 'df-messenger-controls';
          controls.innerHTML = `
            <button class="df-messenger-control-btn" title="Maximizar chat" data-action="maximize">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M8 3H5a2 2 0 0 0-2 2v3m18 0V5a2 2 0 0 0-2-2h-3m0 18h3a2 2 0 0 0 2-2v-3M3 16v3a2 2 0 0 0 2 2h3"/>
              </svg>
            </button>
            <button class="df-messenger-control-btn" title="Cerrar chat" data-action="close">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <line x1="18" y1="6" x2="6" y2="18"></line>
                <line x1="6" y1="6" x2="18" y2="18"></line>
              </svg>
            </button>
          `;

          // Add event listeners
          controls.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();
            const target = e.target as HTMLElement;
            const button = target.closest('[data-action]') as HTMLElement;
            if (button) {
              const action = button.getAttribute('data-action');
              if (action === 'maximize') {
                setIsMaximized(true);
              } else if (action === 'close') {
                onBack();
              }
            }
          });

          messengerElement.appendChild(controls);
        }
      };

      // Wait for widget to be fully rendered and stable
      setTimeout(addControls, 3000);
    }
  }, [isWidget, isLoading, onBack]);

  // Render maximize overlay if needed
  if (isWidget && isMaximized) {
    return (
      <div className="fixed inset-0 z-[9999] bg-black bg-opacity-80 backdrop-blur-sm">
        <div className="w-full h-full bg-white">
          <div className="h-full flex flex-col">
            <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white p-4 flex items-center justify-between">
              <div className="flex items-center">
                <div className="h-8 w-8 rounded-full overflow-hidden bg-white/20 mr-3">
                  <img
                    src={bot.imageUrl}
                    alt={bot.name}
                    className="w-full h-full object-cover"
                  />
                </div>
                <div>
                  <h3 className="font-semibold">{bot.name}</h3>
                  <p className="text-sm opacity-90">Chat en pantalla completa</p>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => setIsMaximized(false)}
                  className="p-2 rounded-lg bg-white/20 hover:bg-white/30 transition-colors touch-target"
                  title="Minimizar"
                >
                  <Minimize2 className="h-4 w-4" />
                </button>
                <button
                  onClick={onBack}
                  className="p-2 rounded-lg bg-white/20 hover:bg-white/30 transition-colors touch-target"
                  title="Cerrar"
                >
                  <X className="h-4 w-4" />
                </button>
              </div>
            </div>
            <div className="flex-1 bg-gray-50 relative" id="maximized-chat-container">
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="text-center p-8">
                  <div className="text-blue-500 mb-4">
                    <MessageCircle size={48} className="mx-auto" />
                  </div>
                  <h3 className="text-lg font-semibold text-gray-800 mb-2">
                    Chat en pantalla completa
                  </h3>
                  <p className="text-gray-600 mb-4">
                    El chat se está cargando en modo maximizado...
                  </p>
                  <button
                    onClick={() => setIsMaximized(false)}
                    className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    Volver al chat normal
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (isWidget) return null;
  
  // Mostrar estado de carga mejorado
  if (isLoading) {
    return (
      <div className="h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center p-8 bg-white rounded-lg shadow-md max-w-md">
          <div className="text-blue-500 mb-4">
            <Loader2 size={48} className="mx-auto animate-spin" />
          </div>
          <h3 className="text-lg font-semibold text-gray-800 mb-2">
            Cargando chat
          </h3>
          <p className="text-gray-600 mb-4">
            {loadingMessage}
          </p>
          {retryAttempt > 0 && (
            <p className="text-sm text-gray-500">
              Intento {retryAttempt + 1} de {MAX_RETRIES + 1}
            </p>
          )}
        </div>
      </div>
    );
  }
  
  // Mostrar estado de error mejorado
  if (error.hasError) {
    const getErrorIcon = () => {
      switch (error.type) {
        case 'NETWORK_ERROR':
          return '🌐';
        case 'TIMEOUT_ERROR':
          return '⏱️';
        case 'CONFIG_NOT_FOUND':
          return '⚙️';
        case 'SCRIPT_LOAD_FAILED':
        case 'WIDGET_INIT_FAILED':
          return '🔧';
        default:
          return '❌';
      }
    };

    const getErrorTitle = () => {
      switch (error.type) {
        case 'NETWORK_ERROR':
          return 'Error de conexión';
        case 'TIMEOUT_ERROR':
          return 'Tiempo de espera agotado';
        case 'CONFIG_NOT_FOUND':
          return 'Configuración no encontrada';
        case 'SCRIPT_LOAD_FAILED':
          return 'Error de carga';
        case 'WIDGET_INIT_FAILED':
          return 'Error de inicialización';
        default:
          return 'Error al cargar el chat';
      }
    };

    return (
      <div className="h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center p-8 bg-white rounded-lg shadow-md max-w-md">
          <div className="text-4xl mb-4">
            {getErrorIcon()}
          </div>
          <h3 className="text-lg font-semibold text-gray-800 mb-2">
            {getErrorTitle()}
          </h3>
          <p className="text-gray-600 mb-4">
            {error.message}
          </p>
          
          {error.retryCount > 0 && (
            <p className="text-sm text-gray-500 mb-4">
              Intentos realizados: {error.retryCount}
            </p>
          )}

          <div className="flex gap-2 justify-center">
            <button
              onClick={onBack}
              className="px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors"
            >
              Volver
            </button>
            
            {retryAttempt < MAX_RETRIES && error.type !== 'CONFIG_NOT_FOUND' && (
              <button
                onClick={handleRetry}
                className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
              >
                Reintentar
              </button>
            )}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="h-screen flex flex-col">
      <div ref={containerRef} className="flex-1" />
    </div>
  );
};

export default DialogflowChat;
