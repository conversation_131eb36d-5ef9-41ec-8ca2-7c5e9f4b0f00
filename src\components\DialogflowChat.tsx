import React, { useEffect, useRef, useState } from 'react';
import { Bot } from '../types/bot';
import { Loader2, MessageCircle } from 'lucide-react';
import { getDialogflowConfig } from '../config/dialogflow';

interface DialogflowChatProps {
  bot: Bot;
  onBack: () => void;
  isWidget?: boolean;
  onShowUnavailable?: () => void;
}

const DialogflowChat: React.FC<DialogflowChatProps> = ({ bot, onBack, isWidget = false }) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    let mounted = true;

    const initializeChat = async () => {
      try {
        if (!mounted) return;

        console.log('[DialogflowChat] MINIMAL APPROACH - Starting initialization');

        const botConfig = getDialogflowConfig(bot.id);
        if (!botConfig) {
          setError('Configuración no encontrada');
          return;
        }

        // Cargar script de Dialogflow si no existe
        if (!document.querySelector('script[src*="dialogflow"]')) {
          const script = document.createElement('script');
          script.src = 'https://www.gstatic.com/dialogflow-console/fast/messenger/bootstrap.js?v=1';
          script.async = true;
          document.head.appendChild(script);

          await new Promise((resolve, reject) => {
            script.onload = resolve;
            script.onerror = reject;
            setTimeout(resolve, 5000); // Timeout fallback
          });
        }

        // Esperar a que df-messenger esté disponible
        let attempts = 0;
        while (attempts < 10 && (!window.customElements || !window.customElements.get('df-messenger'))) {
          await new Promise(resolve => setTimeout(resolve, 500));
          attempts++;
        }

        if (!mounted) return;

        // Crear widget con configuración MÍNIMA
        const messenger = document.createElement('df-messenger');
        messenger.setAttribute('project-id', botConfig.projectId);
        messenger.setAttribute('agent-id', botConfig.agentId);
        messenger.setAttribute('language-code', 'es');
        messenger.setAttribute('chat-title', botConfig.chatTitle);

        if (isWidget) {
          messenger.setAttribute('type', 'floating');
          // CLAVE: NO agregar event listeners que interfieran
          document.body.appendChild(messenger);
          console.log('[DialogflowChat] Widget agregado al body - ENFOQUE MÍNIMO');
        } else {
          messenger.setAttribute('type', 'inline');
          if (containerRef.current) {
            containerRef.current.appendChild(messenger);
          }
        }

        // Esperar inicialización natural
        setTimeout(() => {
          if (mounted) {
            setIsLoading(false);
            console.log('[DialogflowChat] Inicialización completada - ENFOQUE MÍNIMO');
          }
        }, 3000);

      } catch (error) {
        console.error('[DialogflowChat] Error:', error);
        if (mounted) {
          setError('Error al cargar el chat');
          setIsLoading(false);
        }
      }
    };

    initializeChat();

    return () => {
      mounted = false;
      console.log('[DialogflowChat] Cleanup mínimo');
    };
  }, [bot.id, isWidget]);

  // Widget mode - no renderizar nada
  if (isWidget) return null;

  // Loading state
  if (isLoading) {
    return (
      <div className="h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center p-8">
          <Loader2 size={48} className="mx-auto animate-spin text-blue-500 mb-4" />
          <p className="text-gray-600">Cargando chat...</p>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center p-8 bg-white rounded-lg shadow-md max-w-md">
          <MessageCircle size={48} className="mx-auto text-red-500 mb-4" />
          <h3 className="text-lg font-semibold text-gray-800 mb-2">Error</h3>
          <p className="text-gray-600 mb-4">{error}</p>
          <button
            onClick={onBack}
            className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
          >
            Volver
          </button>
        </div>
      </div>
    );
  }

  // Inline mode
  return (
    <div className="h-screen flex flex-col">
      <div ref={containerRef} className="flex-1" />
    </div>
  );
};

export default DialogflowChat;