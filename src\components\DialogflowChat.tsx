import React, { useEffect, useRef, useState } from 'react';
import { Bo<PERSON> } from '../types/bot';
import { ArrowLeft, Minimize2, MessageCircle, Loader2, Sparkles, Maximize2 } from 'lucide-react';
import { getDialogflowConfig } from '../config/dialogflow';
import { useErrorLogger } from '../utils/errorLogger';
import { useConnectionHealth } from '../hooks/useNetworkStatus';

interface DialogflowChatProps {
  bot: Bot;
  onBack: () => void;
  isWidget?: boolean;
  onShowUnavailable?: () => void;
}

// Tipos de errores específicos para mejor manejo
type ErrorType =
  | 'CONFIG_NOT_FOUND'
  | 'SCRIPT_LOAD_FAILED'
  | 'NETWORK_ERROR'
  | 'WIDGET_INIT_FAILED'
  | 'TIMEOUT_ERROR'
  | 'UNKNOWN_ERROR';

// Estado global para el script de Dialogflow
let dialogflowScriptLoaded = false;
let dialogflowScriptLoading = false;
let dialogflowLoadPromise: Promise<void> | null = null;

// Función para cargar el script de Dialogflow de manera singleton
const loadDialogflowScript = (): Promise<void> => {
  // Si ya está cargado, resolver inmediatamente
  if (dialogflowScriptLoaded) {
    return Promise.resolve();
  }

  // Si ya se está cargando, devolver la promesa existente
  if (dialogflowScriptLoading && dialogflowLoadPromise) {
    return dialogflowLoadPromise;
  }

  // Verificar si el script ya existe en el DOM
  const existingScript = document.querySelector('script[src*="df-messenger.js"]');
  if (existingScript && typeof customElements !== 'undefined' && customElements.get('df-messenger')) {
    dialogflowScriptLoaded = true;
    return Promise.resolve();
  }

  dialogflowScriptLoading = true;
  dialogflowLoadPromise = new Promise((resolve, reject) => {
    const script = document.createElement('script');
    script.src = 'https://www.gstatic.com/dialogflow-console/fast/df-messenger/prod/v1/df-messenger.js';
    script.async = true;

    const timeout = setTimeout(() => {
      dialogflowScriptLoading = false;
      dialogflowLoadPromise = null;
      reject(new Error('Timeout loading Dialogflow script'));
    }, 15000);

    script.onload = () => {
      clearTimeout(timeout);
      dialogflowScriptLoaded = true;
      dialogflowScriptLoading = false;
      resolve();
    };

    script.onerror = (error) => {
      clearTimeout(timeout);
      dialogflowScriptLoading = false;
      dialogflowLoadPromise = null;
      reject(error);
    };

    document.head.appendChild(script);
  });

  return dialogflowLoadPromise;
};

interface ErrorState {
  hasError: boolean;
  type: ErrorType | null;
  message: string;
  retryCount: number;
}

const DialogflowChat: React.FC<DialogflowChatProps> = ({ bot, onBack, isWidget = false, onShowUnavailable }) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [loadingMessage, setLoadingMessage] = useState('Inicializando chat...');
  const [error, setError] = useState<ErrorState>({
    hasError: false,
    type: null,
    message: '',
    retryCount: 0
  });
  const [retryAttempt, setRetryAttempt] = useState(0);
  const [isMaximized, setIsMaximized] = useState(false);

  const { logError, logWarn, logInfo } = useErrorLogger();
  const { isHealthy, hasIssues, connectionMessage } = useConnectionHealth();
  
  const MAX_RETRIES = 3;
  const SCRIPT_LOAD_TIMEOUT = 10000; // 10 segundos
  const WIDGET_INIT_TIMEOUT = 5000;  // 5 segundos

  // Función para manejar errores de manera consistente
  const handleError = (type: ErrorType, message: string, originalError?: any) => {
    logError('DialogflowChat', message, originalError, {
      botId: bot.id,
      botName: bot.name,
      errorType: type,
      retryAttempt,
      isWidget,
      connectionStatus: connectionMessage
    });

    // Si es un widget y hay errores críticos, redirigir a pantalla de no disponible
    if (isWidget && (type === 'CONFIG_NOT_FOUND' || type === 'SCRIPT_LOAD_FAILED') && onShowUnavailable) {
      onShowUnavailable();
      return;
    }

    setError({
      hasError: true,
      type,
      message,
      retryCount: retryAttempt
    });
    setIsLoading(false);
    setLoadingMessage('');
  };

  // Función para reiniciar el estado de error
  const resetError = () => {
    setError({
      hasError: false,
      type: null,
      message: '',
      retryCount: 0
    });
  };

  // Función para reintentar la carga
  const handleRetry = () => {
    if (retryAttempt < MAX_RETRIES) {
      logInfo('DialogflowChat', `Reintentando carga del widget (intento ${retryAttempt + 2})`, {
        botId: bot.id,
        previousError: error.type,
        connectionStatus: connectionMessage
      });
      
      setRetryAttempt(prev => prev + 1);
      resetError();
      setIsLoading(true);
      setLoadingMessage('Reintentando conexión...');
    }
  };

  useEffect(() => {
    let mounted = true;

    // Función para limpiar completamente widgets anteriores
    const cleanupPreviousWidgets = () => {
      // Limpiar todos los widgets existentes
      const existingWidgets = document.querySelectorAll('df-messenger');
      existingWidgets.forEach(widget => {
        try {
          if (widget.parentNode) {
            widget.parentNode.removeChild(widget);
          }
        } catch (e) {
          console.warn('[DialogflowChat] Error removing existing widget:', e);
        }
      });

      // Limpiar contenedor si no es widget
      if (!isWidget && containerRef.current) {
        containerRef.current.innerHTML = '';
      }
    };

    const loadDialogflow = async () => {
      const startTime = Date.now();
      try {
        setLoadingMessage('Verificando configuración...');

        // Limpiar widgets anteriores ANTES de crear uno nuevo
        cleanupPreviousWidgets();

        // Obtener la configuración del bot desde el mapa
        const botConfig = getDialogflowConfig(bot.id);
        if (!botConfig) {
          handleError(
            'CONFIG_NOT_FOUND',
            `No se encontró configuración de Dialogflow para el bot: ${bot.id}`
          );
          return;
        }

        // Validar que la configuración tenga todos los campos requeridos
        if (!botConfig.projectId || !botConfig.agentId) {
          handleError(
            'CONFIG_NOT_FOUND',
            `Configuración incompleta para el bot ${bot.name}. Faltan projectId o agentId.`
          );
          return;
        }

        setLoadingMessage('Cargando recursos de Dialogflow...');

        // Verificar conectividad de red básica
        if (!navigator.onLine) {
          handleError(
            'NETWORK_ERROR',
            'No hay conexión a internet. Verifica tu conexión y vuelve a intentar.'
          );
          return;
        }

        // Advertir sobre conexiones lentas
        if (hasIssues) {
          logWarn('DialogflowChat', `Conexión con problemas detectada: ${connectionMessage}`, {
            botId: bot.id,
            connectionStatus: connectionMessage
          });
          setLoadingMessage('Conexión lenta detectada, esto puede tomar más tiempo...');
        }

        // Load Dialogflow default CSS theme con manejo de errores (solo si no existe)
        let cssLink = document.querySelector('link[href*="df-messenger-default.css"]') as HTMLLinkElement;
        if (!cssLink) {
          cssLink = document.createElement('link');
          cssLink.rel = 'stylesheet';
          cssLink.href = 'https://www.gstatic.com/dialogflow-console/fast/df-messenger/prod/v1/themes/df-messenger-default.css';

          cssLink.onerror = () => {
            console.warn('[DialogflowChat] Failed to load CSS, continuing without default theme');
          };

          document.head.appendChild(cssLink);
        }

        setLoadingMessage('Cargando Dialogflow...');

        // Usar el sistema singleton para cargar el script
        try {
          await loadDialogflowScript();

          if (!mounted) return;

          setLoadingMessage('Configurando interfaz de chat...');

          // Verificar que df-messenger esté disponible
          if (typeof customElements === 'undefined' || !customElements.get('df-messenger')) {
            handleError(
              'WIDGET_INIT_FAILED',
              'El componente df-messenger no se cargó correctamente.'
            );
            return;
          }

          // Inject simplified custom CSS variables for solid backgrounds
          const style = document.createElement('style');
          style.textContent = `
            df-messenger {
              --df-messenger-chat-background-color: #F8FAFC;
              --df-messenger-chat-border: 1px solid #E2E8F0;
              --df-messenger-chat-border-radius: 20px;
              --df-messenger-user-message: #4F46E5;
              --df-messenger-bot-message: #ffffff;
              --df-messenger-button-titlebar-color: #ffffff;
              --df-messenger-button-titlebar-font-color: #374151;
              --df-messenger-input-box-color: #ffffff;
              --df-messenger-input-font-color: #374151;
              --df-messenger-input-placeholder-font-color: #9CA3AF;
              --df-messenger-send-icon: #4F46E5;
              --df-messenger-minimized-chat-close-icon-color: #374151;
              --df-messenger-font-color: #374151;
              --df-messenger-font-family: 'Google Sans', system-ui, sans-serif;
            }
          `;
          document.head.appendChild(style);

          setLoadingMessage('Inicializando widget de chat...');

          // Create messenger element
          const messenger = document.createElement('df-messenger');
          
          // Configurar atributos con validación
          messenger.setAttribute('project-id', botConfig.projectId);
          messenger.setAttribute('agent-id', botConfig.agentId);
          messenger.setAttribute('language-code', 'es');
          messenger.setAttribute('max-query-length', '-1');
          messenger.setAttribute('type', isWidget ? 'floating' : 'inline');
          messenger.setAttribute('chat-title', botConfig.chatTitle);

          // Optional: add a box shadow for better separation
          messenger.style.boxShadow = '0 4px 6px rgba(0,0,0,0.1)';
          messenger.style.borderRadius = '20px';

          // Agregar listeners para eventos del widget
          messenger.addEventListener('df-messenger-error', (event: any) => {
            console.error('[DialogflowChat] Widget error:', event.detail);
            if (mounted) {
              handleError(
                'WIDGET_INIT_FAILED',
                `Error en el widget de Dialogflow: ${event.detail?.message || 'Error desconocido'}`
              );
            }
          });

          if (isWidget) {
            document.body.appendChild(messenger);

            // Setup custom controls (close and maximize buttons)
            const setupCustomControls = () => {
              try {
                const root = (document.querySelector('df-messenger') as any)?.shadowRoot;
                const titlebar = root?.querySelector('.df-messenger-titlebar');
                if (titlebar) {
                  // Manejar click en el titlebar para close button
                  titlebar.addEventListener('click', (e: MouseEvent) => {
                    const target = e.target as HTMLElement;
                    if (target.closest('.df-messenger-titlebar-close-button')) onBack();
                  });

                  // Agregar botón de maximizar si no existe
                  if (!titlebar.querySelector('.df-maximize-button')) {
                    const maximizeButton = document.createElement('button');
                    maximizeButton.className = 'df-maximize-button';
                    maximizeButton.innerHTML = `
                      <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M8 3H5a2 2 0 0 0-2 2v3m18 0V5a2 2 0 0 0-2-2h-3m0 18h3a2 2 0 0 0 2-2v-3M3 16v3a2 2 0 0 0 2 2h3"/>
                      </svg>
                    `;
                    maximizeButton.title = 'Maximizar chat';
                    maximizeButton.style.cssText = `
                      background: none;
                      border: none;
                      color: inherit;
                      cursor: pointer;
                      padding: 8px;
                      margin-right: 4px;
                      border-radius: 4px;
                      display: flex;
                      align-items: center;
                      justify-content: center;
                      transition: background-color 0.2s ease;
                    `;

                    // Agregar hover effect
                    maximizeButton.addEventListener('mouseenter', () => {
                      maximizeButton.style.backgroundColor = 'rgba(0, 0, 0, 0.1)';
                    });
                    maximizeButton.addEventListener('mouseleave', () => {
                      maximizeButton.style.backgroundColor = 'transparent';
                    });

                    // Agregar click handler
                    maximizeButton.addEventListener('click', (e) => {
                      e.preventDefault();
                      e.stopPropagation();
                      setIsMaximized(true);
                    });

                    // Insertar el botón antes del botón de cerrar
                    const closeButton = titlebar.querySelector('.df-messenger-titlebar-close-button');
                    if (closeButton) {
                      titlebar.insertBefore(maximizeButton, closeButton);
                    } else {
                      titlebar.appendChild(maximizeButton);
                    }
                  }
                }
              } catch (error) {
                console.warn('[DialogflowChat] Could not setup custom controls:', error);
              }
            };

            setTimeout(setupCustomControls, 1000);
            
          } else if (containerRef.current) {
            containerRef.current.innerHTML = '';
            containerRef.current.appendChild(messenger);
          } else {
            handleError(
              'WIDGET_INIT_FAILED',
              'No se pudo encontrar el contenedor para el widget de chat'
            );
            return;
          }

          // Timeout para la inicialización del widget
          const initTimeout = setTimeout(() => {
            if (mounted) {
              handleError(
                'TIMEOUT_ERROR',
                'El widget de Dialogflow tardó demasiado en inicializarse'
              );
            }
          }, WIDGET_INIT_TIMEOUT);

          // Verificar que el widget se inicializó correctamente
          setTimeout(() => {
            if (mounted) {
              clearTimeout(initTimeout);
              const widgetElement = isWidget 
                ? document.querySelector('df-messenger')
                : containerRef.current?.querySelector('df-messenger');
              
              if (widgetElement) {
                logInfo('DialogflowChat', 'Widget de Dialogflow inicializado correctamente', {
                  botId: bot.id,
                  botName: bot.name,
                  isWidget,
                  retryAttempt,
                  loadTime: Date.now() - startTime
                });
                setLoadingMessage('');
                setIsLoading(false);
              } else {
                handleError(
                  'WIDGET_INIT_FAILED',
                  'El widget de Dialogflow no se inicializó correctamente'
                );
              }
            }
          }, 2000);
        } catch (scriptError) {
          if (mounted) {
            handleError(
              'SCRIPT_LOAD_FAILED',
              'No se pudo cargar el script de Dialogflow. Verifica tu conexión a internet.',
              scriptError
            );
          }
        }

        return () => {
          mounted = false;
          
          // Limpiar recursos de manera segura (no removemos CSS ni script ya que son singleton)
          try {
            document.head.querySelectorAll('style').forEach(el => {
              if (el.textContent?.includes('--df-messenger-chat-background-color')) {
                el.remove();
              }
            });
          } catch (e) {
            console.warn('[DialogflowChat] Error removing custom styles:', e);
          }

          if (isWidget) {
            try {
              const existing = document.querySelector('df-messenger');
              if (existing && document.body.contains(existing)) {
                document.body.removeChild(existing);
              }
            } catch (e) {
              console.warn('[DialogflowChat] Error removing widget:', e);
            }
          }
        };
      } catch (generalError) {
        if (mounted) {
          handleError(
            'UNKNOWN_ERROR',
            'Error inesperado al cargar Dialogflow',
            generalError
          );
        }
      }
    };

    loadDialogflow();
    return () => {
      mounted = false;
      cleanupPreviousWidgets();
    };
  }, [bot.id, isWidget, onBack, retryAttempt]);

  // useEffect para manejar el widget en modo maximizado
  useEffect(() => {
    if (!isWidget || !isMaximized) return;

    const setupMaximizedWidget = async () => {
      try {
        const maximizedContainer = document.getElementById('maximized-chat-container');
        if (!maximizedContainer) return;

        // Buscar el widget existente
        const existingWidget = document.querySelector('df-messenger');
        if (existingWidget) {
          // Crear un clon del widget para el modo maximizado
          const clonedWidget = existingWidget.cloneNode(true) as HTMLElement;

          // Aplicar estilos para maximizado
          clonedWidget.style.cssText = `
            width: 100% !important;
            height: 100% !important;
            border-radius: 0 !important;
            box-shadow: none !important;
            border: none !important;
          `;

          // Limpiar el contenedor y agregar el widget clonado
          maximizedContainer.innerHTML = '';
          maximizedContainer.appendChild(clonedWidget);

          // Ocultar el widget original temporalmente
          existingWidget.style.display = 'none';
        }
      } catch (error) {
        console.warn('[DialogflowChat] Error setting up maximized widget:', error);
      }
    };

    const timer = setTimeout(setupMaximizedWidget, 500);

    return () => {
      clearTimeout(timer);
      // Restaurar el widget original cuando se cierre el modo maximizado
      const existingWidget = document.querySelector('df-messenger');
      if (existingWidget) {
        existingWidget.style.display = '';
      }
    };
  }, [isWidget, isMaximized]);

  // Render del overlay maximizado
  if (isWidget && isMaximized) {
    return (
      <div className="fixed inset-0 z-[9999] bg-black bg-opacity-50 backdrop-blur-sm">
        <div className="w-full h-full bg-white">
          <div className="h-full flex flex-col">
            {/* Header del chat maximizado */}
            <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white p-4 flex items-center justify-between">
              <div className="flex items-center">
                <div className="h-10 w-10 rounded-full overflow-hidden bg-white/20 mr-3">
                  <img
                    src={bot.imageUrl}
                    alt={bot.name}
                    className="w-full h-full object-cover"
                  />
                </div>
                <div>
                  <h3 className="font-semibold text-lg">{bot.name}</h3>
                  <p className="text-sm opacity-90">Chat maximizado</p>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => setIsMaximized(false)}
                  className="p-2 rounded-lg bg-white/20 hover:bg-white/30 transition-colors"
                  title="Minimizar"
                >
                  <Minimize2 className="h-5 w-5" />
                </button>
                <button
                  onClick={onBack}
                  className="p-2 rounded-lg bg-white/20 hover:bg-white/30 transition-colors"
                  title="Cerrar chat"
                >
                  <MessageCircle className="h-5 w-5" />
                </button>
              </div>
            </div>

            {/* Contenedor del chat maximizado */}
            <div className="flex-1 bg-gray-50 relative overflow-hidden">
              <div className="absolute inset-0" id="maximized-chat-container">
                <div className="h-full flex items-center justify-center">
                  <div className="text-center p-8">
                    <div className="text-blue-500 mb-4">
                      <Sparkles size={48} className="mx-auto animate-pulse" />
                    </div>
                    <h3 className="text-xl font-semibold text-gray-800 mb-2">
                      Chat Maximizado
                    </h3>
                    <p className="text-gray-600 mb-4">
                      El chat se está cargando en modo pantalla completa...
                    </p>
                    <button
                      onClick={() => setIsMaximized(false)}
                      className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                    >
                      Volver al chat normal
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (isWidget) return null;
  
  // Mostrar estado de carga mejorado
  if (isLoading) {
    return (
      <div className="h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center p-8 bg-white rounded-lg shadow-md max-w-md">
          <div className="text-blue-500 mb-4">
            <Loader2 size={48} className="mx-auto animate-spin" />
          </div>
          <h3 className="text-lg font-semibold text-gray-800 mb-2">
            Cargando chat
          </h3>
          <p className="text-gray-600 mb-4">
            {loadingMessage}
          </p>
          {retryAttempt > 0 && (
            <p className="text-sm text-gray-500">
              Intento {retryAttempt + 1} de {MAX_RETRIES + 1}
            </p>
          )}
        </div>
      </div>
    );
  }
  
  // Mostrar estado de error mejorado
  if (error.hasError) {
    const getErrorIcon = () => {
      switch (error.type) {
        case 'NETWORK_ERROR':
          return '🌐';
        case 'TIMEOUT_ERROR':
          return '⏱️';
        case 'CONFIG_NOT_FOUND':
          return '⚙️';
        case 'SCRIPT_LOAD_FAILED':
        case 'WIDGET_INIT_FAILED':
          return '🔧';
        default:
          return '❌';
      }
    };

    const getErrorTitle = () => {
      switch (error.type) {
        case 'NETWORK_ERROR':
          return 'Error de conexión';
        case 'TIMEOUT_ERROR':
          return 'Tiempo de espera agotado';
        case 'CONFIG_NOT_FOUND':
          return 'Configuración no encontrada';
        case 'SCRIPT_LOAD_FAILED':
          return 'Error de carga';
        case 'WIDGET_INIT_FAILED':
          return 'Error de inicialización';
        default:
          return 'Error al cargar el chat';
      }
    };

    return (
      <div className="h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center p-8 bg-white rounded-lg shadow-md max-w-md">
          <div className="text-4xl mb-4">
            {getErrorIcon()}
          </div>
          <h3 className="text-lg font-semibold text-gray-800 mb-2">
            {getErrorTitle()}
          </h3>
          <p className="text-gray-600 mb-4">
            {error.message}
          </p>
          
          {error.retryCount > 0 && (
            <p className="text-sm text-gray-500 mb-4">
              Intentos realizados: {error.retryCount}
            </p>
          )}

          <div className="flex gap-2 justify-center">
            <button
              onClick={onBack}
              className="px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors"
            >
              Volver
            </button>
            
            {retryAttempt < MAX_RETRIES && error.type !== 'CONFIG_NOT_FOUND' && (
              <button
                onClick={handleRetry}
                className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
              >
                Reintentar
              </button>
            )}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="h-screen flex flex-col">
      <div ref={containerRef} className="flex-1" />
    </div>
  );
};

export default DialogflowChat;
