import { useState, useEffect } from 'react';
import { hasDialogflowConfig } from '../config/dialogflow';

export type BotStatus = 'available' | 'unavailable' | 'maintenance' | 'error' | 'checking';

export interface BotAvailability {
  status: BotStatus;
  message?: string;
  lastChecked?: Date;
  canUseFallback: boolean;
}

/**
 * Hook para verificar la disponibilidad de un bot
 * Incluye verificación de configuración, estado de red y fallbacks
 */
export const useBotAvailability = (botId: string) => {
  const [availability, setAvailability] = useState<BotAvailability>({
    status: 'checking',
    canUseFallback: true
  });

  useEffect(() => {
    const checkBotAvailability = async () => {
      try {
        setAvailability(prev => ({ ...prev, status: 'checking' }));

        // 1. Verificar si tiene configuración de Dialogflow
        const hasConfig = hasDialogflowConfig(botId);
        if (!hasConfig) {
          setAvailability({
            status: 'unavailable',
            message: 'Este bot no tiene configuración de Dialogflow disponible',
            lastChecked: new Date(),
            canUseFallback: true
          });
          return;
        }

        // 2. Verificar conectividad básica
        if (!navigator.onLine) {
          setAvailability({
            status: 'error',
            message: 'Sin conexión a internet',
            lastChecked: new Date(),
            canUseFallback: true
          });
          return;
        }

        // 3. Verificar si Dialogflow está disponible (ping básico)
        try {
          // Intentar cargar el script de Dialogflow para verificar disponibilidad
          const response = await fetch('https://www.gstatic.com/dialogflow-console/fast/df-messenger/prod/v1/df-messenger.js', {
            method: 'HEAD',
            mode: 'no-cors'
          });
          
          setAvailability({
            status: 'available',
            message: 'Bot disponible y listo para usar',
            lastChecked: new Date(),
            canUseFallback: true
          });
        } catch (error) {
          setAvailability({
            status: 'error',
            message: 'Servicio de Dialogflow no disponible temporalmente',
            lastChecked: new Date(),
            canUseFallback: true
          });
        }

      } catch (error) {
        setAvailability({
          status: 'error',
          message: 'Error al verificar disponibilidad del bot',
          lastChecked: new Date(),
          canUseFallback: true
        });
      }
    };

    checkBotAvailability();

    // Verificar cada 30 segundos si hay errores
    const interval = setInterval(() => {
      if (availability.status === 'error' || availability.status === 'unavailable') {
        checkBotAvailability();
      }
    }, 30000);

    return () => clearInterval(interval);
  }, [botId]);

  const retry = () => {
    setAvailability(prev => ({ ...prev, status: 'checking' }));
    // El useEffect se encargará de la verificación
  };

  return {
    ...availability,
    retry
  };
};

/**
 * Hook para obtener el estado de disponibilidad de múltiples bots
 */
export const useMultipleBotAvailability = (botIds: string[]) => {
  const [availabilities, setAvailabilities] = useState<Record<string, BotAvailability>>({});

  useEffect(() => {
    const checkAllBots = async () => {
      const results: Record<string, BotAvailability> = {};
      
      for (const botId of botIds) {
        const hasConfig = hasDialogflowConfig(botId);
        results[botId] = {
          status: hasConfig ? 'available' : 'unavailable',
          message: hasConfig ? 'Disponible' : 'Sin configuración',
          lastChecked: new Date(),
          canUseFallback: true
        };
      }
      
      setAvailabilities(results);
    };

    checkAllBots();
  }, [botIds]);

  return availabilities;
};
