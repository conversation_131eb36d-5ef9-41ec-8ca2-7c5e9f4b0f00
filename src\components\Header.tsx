import React from 'react';
import { Search, Menu } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

interface HeaderProps {
  searchTerm: string;
  onSearchChange: (term: string) => void;
  onMenuToggle: () => void;
  showSearch?: boolean;
}

const Header: React.FC<HeaderProps> = ({ searchTerm, onSearchChange, onMenuToggle, showSearch = true }) => {
  const navigate = useNavigate();

  const handleLogoClick = () => {
    navigate('/');
  };

  return (
    <header className="bg-white border-b border-gray-200 fixed top-0 left-0 right-0 z-50">
      <div className="h-16 px-6 flex items-center">
        {/* Left Section */}
        <div className="flex items-center flex-1">
          <button
            onClick={onMenuToggle}
            className="lg:hidden p-2 rounded-lg text-gray-600 hover:text-gray-900 hover:bg-gray-100 transition-colors mr-4"
          >
            <Menu className="h-6 w-6" />
          </button>
          
          <div className="flex items-center space-x-3">
            <div className="h-10 flex items-center">
              <img
                src="https://crossnet.la/wp-content/uploads/2024/10/logo-crossnet_header.svg"
                alt="Crossnet"
                className="h-8 w-auto cursor-pointer hover:opacity-80 transition-opacity"
                onClick={handleLogoClick}
              />
            </div>
            <div>
              <h1 className="text-xl font-bold text-gray-900"></h1>
              <p className="text-xs text-gray-500"></p>
            </div>
          </div>
        </div>
        
        {/* Center Section - Search */}
        {showSearch && (
          <div className="flex-1 flex justify-center">
            <div className="w-full max-w-md">
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Search className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  type="text"
                  value={searchTerm}
                  onChange={(e) => onSearchChange(e.target.value)}
                  className="block w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg bg-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-center"
                  placeholder="Buscar bots..."
                />
              </div>
            </div>
          </div>
        )}

        {/* Right Section */}
        <div className="flex items-center justify-end flex-1">
          <div className="h-8 w-8 bg-white-300 rounded-full"></div>
        </div>
      </div>
    </header>
  );
};

export default Header;