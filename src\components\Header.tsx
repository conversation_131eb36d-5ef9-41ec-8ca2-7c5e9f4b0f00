import React, { useState } from 'react';
import { Search, Menu, Settings } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import PDFViewer from './PDFViewer';

interface HeaderProps {
  searchTerm: string;
  onSearchChange: (term: string) => void;
  onMenuToggle: () => void;
  showSearch?: boolean;
}

const Header: React.FC<HeaderProps> = ({ searchTerm, onSearchChange, onMenuToggle, showSearch = true }) => {
  const navigate = useNavigate();
  const [isPDFViewerOpen, setIsPDFViewerOpen] = useState(false);

  const pdfUrl = 'https://d2hwq5exue146r.cloudfront.net/crossnet/Configuraci%C3%B3ngenesyscloud.pdf';

  const handleLogoClick = () => {
    navigate('/');
  };

  const handleConfigClick = () => {
    setIsPDFViewerOpen(true);
  };

  return (
    <header className="bg-white border-b border-gray-200 fixed top-0 left-0 right-0 z-50">
      <div className="h-16 px-4 sm:px-6 flex items-center">
        {/* Left Section */}
        <div className="flex items-center flex-1">
          <button
            onClick={onMenuToggle}
            className="lg:hidden p-2 rounded-lg text-gray-600 hover:text-gray-900 hover:bg-gray-100 transition-colors mr-2 sm:mr-4"
          >
            <Menu className="h-5 w-5 sm:h-6 sm:w-6" />
          </button>

          <div className="flex items-center space-x-2 sm:space-x-3">
            <div className="h-8 sm:h-10 flex items-center">
              <img
                src="https://crossnet.la/wp-content/uploads/2024/10/logo-crossnet_header.svg"
                alt="Crossnet"
                className="h-6 sm:h-8 w-auto cursor-pointer hover:opacity-80 transition-opacity"
                onClick={handleLogoClick}
              />
            </div>
            <div className="hidden sm:block">
              <h1 className="text-xl font-bold text-gray-900"></h1>
              <p className="text-xs text-gray-500"></p>
            </div>
          </div>
        </div>
        
        {/* Center Section - Search */}
        {showSearch && (
          <div className="flex-1 flex justify-center mx-2 sm:mx-4">
            <div className="w-full max-w-xs sm:max-w-md">
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-2 sm:pl-3 flex items-center pointer-events-none">
                  <Search className="h-4 w-4 sm:h-5 sm:w-5 text-gray-400" />
                </div>
                <input
                  type="text"
                  value={searchTerm}
                  onChange={(e) => onSearchChange(e.target.value)}
                  className="block w-full pl-8 sm:pl-10 pr-3 sm:pr-4 py-1.5 sm:py-2 text-sm sm:text-base border border-gray-300 rounded-lg bg-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-center"
                  placeholder="Buscar bots..."
                />
              </div>
            </div>
          </div>
        )}

        {/* Right Section */}
        <div className="flex items-center justify-end flex-1">
          <button
            onClick={handleConfigClick}
            className="h-8 w-8 sm:h-10 sm:w-10 bg-gradient-to-br from-blue-50 to-indigo-100 hover:from-blue-100 hover:to-indigo-200 rounded-full flex items-center justify-center transition-all duration-200 hover:scale-105 shadow-sm hover:shadow-md border border-blue-200/50"
            title="Configuración de Genesys Cloud"
          >
            <Settings className="h-4 w-4 sm:h-5 sm:w-5 text-blue-600" />
          </button>
        </div>
      </div>

      {/* PDF Viewer Modal */}
      <PDFViewer
        isOpen={isPDFViewerOpen}
        onClose={() => setIsPDFViewerOpen(false)}
        pdfUrl={pdfUrl}
        title="Configuración de Genesys Cloud"
      />
    </header>
  );
};

export default Header;