import React, { useEffect } from 'react';
import { Routes, Route, useParams, Navigate, useSearchParams } from 'react-router-dom';
import { bots } from '../data/bots';
import HomePage from './HomePage';
import BotDetail from './BotDetail';
import ChatInterface from './ChatInterface';
import DialogflowChat from './DialogflowChat';
import BotUnavailableScreen from './BotUnavailableScreen';
import { hasDialogflowConfig } from '../config/dialogflow';
import { useBotAvailability } from '../hooks/useBotAvailability';

interface AppRouterProps {
  searchTerm: string;
  onBotDetail: (botId: string) => void;
  onBotDemo: (botId: string) => void;
  onStartChat: (botId: string) => void;
  onUseFallback: () => void;
  isDialogflowWidgetOpen: boolean;
  setIsDialogflowWidgetOpen: (open: boolean) => void;
  dialogflowKey: number;
}

// Componente para la página de detalle del bot
const BotDetailPage: React.FC<{
  onStartChat: (botId: string) => void;
  onUseFallback: () => void;
  isDialogflowWidgetOpen: boolean;
  setIsDialogflowWidgetOpen: (open: boolean) => void;
  dialogflowKey: number;
}> = ({ onStartChat, onUseFallback, isDialogflowWidgetOpen, setIsDialogflowWidgetOpen, dialogflowKey }) => {
  const { botId } = useParams<{ botId: string }>();
  const [searchParams, setSearchParams] = useSearchParams();

  if (!botId) {
    return <Navigate to="/" replace />;
  }

  const bot = bots.find(b => b.id === botId);
  if (!bot) {
    return <Navigate to="/" replace />;
  }

  const { status: botStatus, retry: retryBot } = useBotAvailability(botId);

  // Detectar si se debe abrir el chat automáticamente
  useEffect(() => {
    const shouldOpenChat = searchParams.get('chat') === 'true';
    if (shouldOpenChat && hasDialogflowConfig(botId)) {
      console.log(`[BotDetailPage] Auto-opening chat for bot ${botId}`);
      setIsDialogflowWidgetOpen(true);
      // Limpiar el parámetro de la URL
      setSearchParams({});
    }
  }, [botId, searchParams, setIsDialogflowWidgetOpen, setSearchParams]);

  // Si el bot no está disponible y tiene Dialogflow, mostrar pantalla de no disponible
  if (botStatus === 'unavailable' && hasDialogflowConfig(botId)) {
    return (
      <BotUnavailableScreen
        bot={bot}
        onBack={() => window.history.back()}
        onUseFallback={onUseFallback}
        onRetry={retryBot}
        status={botStatus}
      />
    );
  }

  console.log(`[BotDetailPage] Rendering for bot ${botId}, isDialogflowWidgetOpen: ${isDialogflowWidgetOpen}, hasDialogflowConfig: ${hasDialogflowConfig(botId)}`);

  return (
    <>
      <BotDetail
        bot={bot}
        onBack={() => window.history.back()}
        onStartChat={onStartChat}
      />
      {isDialogflowWidgetOpen && hasDialogflowConfig(botId) && (
        <DialogflowChat
          key={dialogflowKey}
          bot={bot}
          onBack={() => setIsDialogflowWidgetOpen(false)}
          isWidget={true}
        />
      )}
    </>
  );
};

// Componente para la página de chat del bot
const BotChatPage: React.FC = () => {
  const { botId } = useParams<{ botId: string }>();
  
  if (!botId) {
    return <Navigate to="/" replace />;
  }

  const bot = bots.find(b => b.id === botId);
  if (!bot) {
    return <Navigate to="/" replace />;
  }

  // Si el bot tiene Dialogflow, redirigir a la página de detalle
  if (hasDialogflowConfig(botId)) {
    return <Navigate to={`/bot/${botId}`} replace />;
  }

  return (
    <ChatInterface
      bot={bot}
      onBack={() => window.history.back()}
    />
  );
};

const AppRouter: React.FC<AppRouterProps> = ({
  searchTerm,
  onBotDetail,
  onBotDemo,
  onStartChat,
  onUseFallback,
  isDialogflowWidgetOpen,
  setIsDialogflowWidgetOpen,
  dialogflowKey
}) => {
  return (
    <Routes>
      <Route 
        path="/" 
        element={
          <HomePage
            bots={bots}
            searchTerm={searchTerm}
            onBotDetail={onBotDetail}
            onBotDemo={onBotDemo}
          />
        } 
      />
      <Route 
        path="/bot/:botId" 
        element={
          <BotDetailPage
            onStartChat={onStartChat}
            onUseFallback={onUseFallback}
            isDialogflowWidgetOpen={isDialogflowWidgetOpen}
            setIsDialogflowWidgetOpen={setIsDialogflowWidgetOpen}
            dialogflowKey={dialogflowKey}
          />
        } 
      />
      <Route 
        path="/bot/:botId/chat" 
        element={<BotChatPage />} 
      />
      {/* Ruta catch-all para redirigir a home */}
      <Route path="*" element={<Navigate to="/" replace />} />
    </Routes>
  );
};

export default AppRouter;
