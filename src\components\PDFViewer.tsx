import React from 'react';
import { X, ExternalLink, Download, FileText } from 'lucide-react';

interface PDFViewerProps {
  isOpen: boolean;
  onClose: () => void;
  pdfUrl: string;
  title?: string;
}

const PDFViewer: React.FC<PDFViewerProps> = ({
  isOpen,
  onClose,
  pdfUrl,
  title = "Documento PDF"
}) => {
  if (!isOpen) return null;

  const handleDownload = () => {
    const link = document.createElement('a');
    link.href = pdfUrl;
    link.download = title.replace(/\s+/g, '_') + '.pdf';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const handleOpenInNewTab = () => {
    window.open(pdfUrl, '_blank');
  };

  return (
    <div className="fixed inset-0 z-[100] overflow-hidden" style={{ zIndex: 100 }}>
      {/* Backdrop */}
      <div
        className="absolute inset-0 bg-black/60 backdrop-blur-sm transition-opacity"
        onClick={onClose}
      />

      {/* Modal */}
      <div className="relative h-full flex items-center justify-center p-4">
        <div className="bg-white rounded-2xl shadow-2xl w-full max-w-6xl h-full max-h-[90vh] flex flex-col overflow-hidden relative z-[101]" style={{ zIndex: 101 }}>
          
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-gray-200 bg-gradient-to-r from-blue-50 to-indigo-50">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-blue-100 rounded-lg">
                <FileText className="h-6 w-6 text-blue-600" />
              </div>
              <div>
                <h2 className="text-xl font-bold text-gray-900">{title}</h2>
                <p className="text-sm text-gray-600">Configuración de Genesys Cloud</p>
              </div>
            </div>
            
            <div className="flex items-center space-x-2">
              {/* Download Button */}
              <button
                onClick={handleDownload}
                className="inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors font-medium text-sm"
                title="Descargar PDF"
              >
                <Download className="h-4 w-4 mr-2" />
                Descargar
              </button>
              
              {/* Open in New Tab Button */}
              <button
                onClick={handleOpenInNewTab}
                className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium text-sm"
                title="Abrir en nueva pestaña"
              >
                <ExternalLink className="h-4 w-4 mr-2" />
                Nueva pestaña
              </button>
              
              {/* Close Button */}
              <button
                onClick={onClose}
                className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
                title="Cerrar"
              >
                <X className="h-6 w-6" />
              </button>
            </div>
          </div>
          
          {/* PDF Content */}
          <div className="flex-1 relative bg-gray-100">
            <iframe
              src={`${pdfUrl}#toolbar=1&navpanes=1&scrollbar=1&page=1&view=FitH`}
              className="w-full h-full border-0"
              title={title}
              loading="lazy"
              onLoad={() => {
                // Hide loading overlay when PDF loads
                const overlay = document.querySelector('.pdf-loading-overlay');
                if (overlay) {
                  (overlay as HTMLElement).style.display = 'none';
                }
              }}
            />

            {/* Loading overlay */}
            <div className="pdf-loading-overlay absolute inset-0 flex items-center justify-center bg-white/90 backdrop-blur-sm transition-opacity duration-300">
              <div className="text-center">
                <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mb-4"></div>
                <p className="text-gray-600 font-medium">Cargando documento...</p>
                <p className="text-sm text-gray-500 mt-2">Configuración de Genesys Cloud</p>
              </div>
            </div>

            {/* Fallback message for mobile */}
            <div className="absolute inset-0 flex items-center justify-center bg-blue-50 md:hidden">
              <div className="text-center p-6">
                <FileText className="h-16 w-16 text-blue-600 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Vista móvil</h3>
                <p className="text-gray-600 mb-4">Para una mejor experiencia, usa los botones de arriba:</p>
                <div className="space-y-2">
                  <button
                    onClick={handleOpenInNewTab}
                    className="w-full inline-flex items-center justify-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium"
                  >
                    <ExternalLink className="h-4 w-4 mr-2" />
                    Abrir en nueva pestaña
                  </button>
                  <button
                    onClick={handleDownload}
                    className="w-full inline-flex items-center justify-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors font-medium"
                  >
                    <Download className="h-4 w-4 mr-2" />
                    Descargar PDF
                  </button>
                </div>
              </div>
            </div>
          </div>
          
          {/* Footer */}
          <div className="p-4 border-t border-gray-200 bg-gray-50">
            <div className="flex items-center justify-between text-sm text-gray-600">
              <div className="flex items-center space-x-4">
                <span>📄 Documento de configuración oficial</span>
                <span>•</span>
                <span>🔒 Contenido seguro</span>
              </div>
              <div className="flex items-center space-x-2">
                <span>Powered by</span>
                <span className="font-semibold text-blue-600">Crossnet</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PDFViewer;
