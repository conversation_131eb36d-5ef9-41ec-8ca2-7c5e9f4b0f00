export interface Bot {
  id: string;
  name: string;
  description: string;
  fullDescription: string;
  category: string;
  endpoint?: string;
  contactNumber?: string;
  conversationFlow: ConversationStep[];
  icon: string;
  color: string;
  imageUrl: string;
}

export interface ConversationStep {
  userInput: string;
  botResponse: string;
  expectedResult: string;
}

export interface Message {
  id: string;
  content: string;
  sender: 'user' | 'bot';
  timestamp: Date;
}
</parameter>