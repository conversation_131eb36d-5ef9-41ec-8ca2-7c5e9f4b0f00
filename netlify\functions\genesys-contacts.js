// Netlify Function para manejar creación de contactos en Genesys Cloud
exports.handler = async (event, context) => {
  // Solo permitir POST
  if (event.httpMethod !== 'POST') {
    return {
      statusCode: 405,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        'Access-Control-Allow-Methods': 'POST, OPTIONS'
      },
      body: JSON.stringify({ error: 'Method not allowed' })
    };
  }

  // Manejar preflight OPTIONS request
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        'Access-Control-Allow-Methods': 'POST, OPTIONS'
      },
      body: ''
    };
  }

  try {
    console.log('📞 Creando contacto en Genesys Cloud...');
    
    // Obtener datos del request
    const requestBody = JSON.parse(event.body);
    const { token, contactData } = requestBody;
    
    if (!token || !contactData) {
      return {
        statusCode: 400,
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          error: 'Token y contactData son requeridos'
        })
      };
    }

    const CONTACT_LIST_ID = '6b13023c-b8fa-4012-91cf-69b427275448';
    const url = `https://api.mypurecloud.com/api/v2/outbound/contactlists/${CONTACT_LIST_ID}/contacts`;
    
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify([contactData])
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Error al crear contacto:', {
        status: response.status,
        statusText: response.statusText,
        body: errorText
      });
      
      return {
        statusCode: response.status,
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          error: `Error ${response.status}: ${response.statusText}`,
          details: errorText
        })
      };
    }

    const responseData = await response.json();
    console.log('✅ Contacto creado exitosamente');
    
    return {
      statusCode: 200,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(responseData)
    };

  } catch (error) {
    console.error('💥 Error en función de contactos:', error);
    
    return {
      statusCode: 500,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        error: 'Error interno del servidor',
        details: error.message
      })
    };
  }
};
