import React, { memo } from 'react';
import {
  Check<PERSON>ircle,
  XCircle,
  AlertTriangle,
  Loader2,
  <PERSON>fresh<PERSON>w,
  Wifi,
  WifiOff
} from 'lucide-react';
import { BotStatus } from '../hooks/useBotAvailability';

interface BotStatusIndicatorProps {
  status: BotStatus;
  message?: string;
  showText?: boolean;
  size?: 'sm' | 'md' | 'lg';
  onRetry?: () => void;
}

const BotStatusIndicator: React.FC<BotStatusIndicatorProps> = ({
  status,
  message,
  showText = false,
  size = 'md',
  onRetry
}) => {
  const getStatusConfig = () => {
    switch (status) {
      case 'available':
        return {
          icon: CheckCircle,
          color: 'text-green-500',
          bgColor: 'bg-green-100',
          text: 'Disponible',
          description: message || 'Bot funcionando correctamente'
        };
      case 'unavailable':
        return {
          icon: XCircle,
          color: 'text-gray-500',
          bgColor: 'bg-gray-100',
          text: 'No disponible',
          description: message || 'Bot no configurado'
        };
      case 'maintenance':
        return {
          icon: AlertTriangle,
          color: 'text-yellow-500',
          bgColor: 'bg-yellow-100',
          text: 'Mantenimiento',
          description: message || 'Bot en mantenimiento programado'
        };
      case 'error':
        return {
          icon: WifiOff,
          color: 'text-red-500',
          bgColor: 'bg-red-100',
          text: 'Error',
          description: message || 'Error de conexión'
        };
      case 'checking':
        return {
          icon: Loader2,
          color: 'text-blue-500',
          bgColor: 'bg-blue-100',
          text: 'Verificando...',
          description: 'Verificando disponibilidad del bot'
        };
      default:
        return {
          icon: AlertTriangle,
          color: 'text-gray-500',
          bgColor: 'bg-gray-100',
          text: 'Desconocido',
          description: 'Estado desconocido'
        };
    }
  };

  const getSizeClasses = () => {
    switch (size) {
      case 'sm':
        return {
          icon: 'h-3 w-3',
          container: 'h-6 w-6',
          text: 'text-xs'
        };
      case 'lg':
        return {
          icon: 'h-6 w-6',
          container: 'h-10 w-10',
          text: 'text-base'
        };
      default:
        return {
          icon: 'h-4 w-4',
          container: 'h-8 w-8',
          text: 'text-sm'
        };
    }
  };

  const config = getStatusConfig();
  const sizeClasses = getSizeClasses();
  const Icon = config.icon;

  if (!showText) {
    return (
      <div 
        className={`
          ${sizeClasses.container} 
          ${config.bgColor} 
          rounded-full 
          flex 
          items-center 
          justify-center
          ${onRetry && status === 'error' ? 'cursor-pointer hover:opacity-80' : ''}
        `}
        onClick={onRetry && status === 'error' ? onRetry : undefined}
        title={config.description}
      >
        <Icon 
          className={`${sizeClasses.icon} ${config.color} ${status === 'checking' ? 'animate-spin' : ''}`} 
        />
      </div>
    );
  }

  return (
    <div className="flex items-center space-x-2">
      <div 
        className={`
          ${sizeClasses.container} 
          ${config.bgColor} 
          rounded-full 
          flex 
          items-center 
          justify-center
        `}
      >
        <Icon 
          className={`${sizeClasses.icon} ${config.color} ${status === 'checking' ? 'animate-spin' : ''}`} 
        />
      </div>
      
      <div className="flex-1">
        <div className={`font-medium ${config.color} ${sizeClasses.text}`}>
          {config.text}
        </div>
        {message && (
          <div className={`text-gray-600 ${size === 'sm' ? 'text-xs' : 'text-sm'}`}>
            {message}
          </div>
        )}
      </div>

      {onRetry && (status === 'error' || status === 'unavailable') && (
        <button
          onClick={onRetry}
          className="p-1 text-gray-400 hover:text-gray-600 transition-colors"
          title="Reintentar"
        >
          <RefreshCw className="h-4 w-4" />
        </button>
      )}
    </div>
  );
};

export default memo(BotStatusIndicator);
