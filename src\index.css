@tailwind base;
@tailwind components;
@tailwind utilities;

/* Mobile-first native app styles */
@layer base {
  html {
    /* Prevent horizontal scroll */
    overflow-x: hidden;
    /* Enable smooth scrolling */
    scroll-behavior: smooth;
    /* Optimize for mobile */
    -webkit-text-size-adjust: 100%;
    -webkit-tap-highlight-color: transparent;
  }

  body {
    /* Prevent horizontal scroll */
    overflow-x: hidden;
    /* Native app feel */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    /* Prevent zoom on input focus (iOS) */
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    user-select: none;
  }

  /* Allow text selection for specific elements */
  input, textarea, [contenteditable] {
    -webkit-user-select: text;
    user-select: text;
  }

  /* Prevent horizontal overflow on all elements */
  * {
    box-sizing: border-box;
  }

  /* Root container */
  #root {
    overflow-x: hidden;
    min-height: 100vh;
    min-height: 100dvh; /* Dynamic viewport height for mobile */
  }
}

/* Mobile-specific optimizations */
@layer utilities {
  /* Safe area support for iOS */
  .safe-top {
    padding-top: env(safe-area-inset-top);
  }

  .safe-bottom {
    padding-bottom: env(safe-area-inset-bottom);
  }

  .safe-left {
    padding-left: env(safe-area-inset-left);
  }

  .safe-right {
    padding-right: env(safe-area-inset-right);
  }

  /* Native mobile touch targets */
  .touch-target {
    min-height: 44px;
    min-width: 44px;
  }

  /* Prevent text overflow */
  .text-ellipsis-mobile {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: 100%;
  }

  /* Mobile-optimized scrolling */
  .scroll-smooth-mobile {
    -webkit-overflow-scrolling: touch;
    scroll-behavior: smooth;
  }
}
