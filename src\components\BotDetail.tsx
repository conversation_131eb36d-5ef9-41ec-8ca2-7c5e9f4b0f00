import React, { useState } from 'react';
import { Bot } from '../types/bot';
import { ArrowLeft, ExternalLink, Phone, Play, MessageSquare, CheckCircle, RefreshCw, UserPlus } from 'lucide-react';
import { useBotAvailability } from '../hooks/useBotAvailability';
import BotStatusIndicator from './BotStatusIndicator';
import ReactCountryFlag from 'react-country-flag';
import { useNavigate } from 'react-router-dom';
import ContactFormModal from './ContactFormModal';
import { createContact, testAuthentication, type ContactData } from '../services/genesysCloudApi';

interface BotDetailProps {
  bot: Bot;
  onBack: () => void;
  onStartChat: (botId: string) => void;
}

const BotDetail: React.FC<BotDetailProps> = ({ bot, onBack, onStartChat }) => {
  const { status, message, retry } = useBotAvailability(bot.id);
  const navigate = useNavigate();
  const [isContactModalOpen, setIsContactModalOpen] = useState(false);

  // Números de teléfono internacionales con códigos de país
  const phoneNumbers = [
    { country: 'CL', countryName: 'Chile', number: '******-CHILE' },
    { country: 'AR', countryName: 'Argentina', number: '******-ARGENTINA' },
    { country: 'MX', countryName: 'México', number: '******-MEXICO' },
    { country: 'PE', countryName: 'Perú', number: '******-PERU' },
    { country: 'CO', countryName: 'Colombia', number: '******-COLOMBIA' }
  ];

  const handleBackToIndex = () => {
    navigate('/');
  };

  const handleContactSubmit = async (contactData: ContactData) => {
    try {
      console.log('Enviando contacto a Genesys Cloud:', contactData);
      const result = await createContact(contactData);

      if (result.success) {
        console.log('Contacto creado exitosamente:', result.data);
        // Aquí podrías mostrar una notificación de éxito adicional si lo deseas
      } else {
        console.error('Error al crear contacto:', result.message);
        throw new Error(result.message);
      }
    } catch (error) {
      console.error('Error en handleContactSubmit:', error);
      throw error; // Re-lanzar el error para que el modal lo maneje
    }
  };

  const isLocalDevelopment = window.location.hostname === 'localhost';

  const handleTestAuth = async () => {
    console.log('🧪 Iniciando prueba de autenticación...');
    const result = await testAuthentication();
    console.log('📊 Resultado de prueba:', result);

    if (result.success) {
      alert('✅ Autenticación exitosa! Revisa la consola para más detalles.');
    } else {
      alert(`❌ Error de autenticación: ${result.details.error}\n\nRevisa la consola para más detalles.`);
    }
  };

  return (
    <div className="min-h-screen bg-white overflow-x-hidden">
      <div className="px-4 py-4 sm:px-8 sm:py-8">
        <div className="mb-4 sm:mb-8">
          <button
            onClick={handleBackToIndex}
            className="flex items-center text-blue-600 hover:text-blue-700 font-medium transition-colors touch-target"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Volver a la lista
          </button>
        </div>

        <div className="bg-white rounded-lg shadow border border-gray-200 overflow-hidden">
          {/* Header */}
          <div className="relative h-48 sm:h-64 overflow-hidden">
            <img
              src={bot.imageUrl}
              alt={bot.name}
              className="w-full h-full object-cover"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/20 to-transparent"></div>
            <div className="absolute bottom-4 left-4 right-4 sm:bottom-6 sm:left-8 sm:right-8">
              {/* Mobile-first layout */}
              <div className="flex flex-col space-y-4 sm:flex-row sm:items-end sm:justify-between sm:space-y-0">
                <div className="flex-1">
                  <div className="flex flex-col sm:flex-row sm:items-center mb-2">
                    <h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-white mb-2 sm:mb-0 sm:mr-3 text-ellipsis-mobile">{bot.name}</h1>
                    <BotStatusIndicator
                      status={status}
                      message={message}
                      size="sm"
                      onRetry={retry}
                    />
                  </div>
                  <div className="flex flex-wrap items-center gap-2">
                    <span className="inline-flex items-center px-2 py-1 sm:px-3 sm:py-1 rounded-full text-xs sm:text-sm font-medium bg-white/20 text-white border border-white/30">
                      {bot.category}
                    </span>
                    {status !== 'available' && (
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-500/20 text-red-100 border border-red-300/30">
                        {status === 'error' ? 'Error' : status === 'unavailable' ? 'No disponible' : 'Verificando...'}
                      </span>
                    )}
                  </div>
                </div>
                <div className="flex flex-col space-y-2 sm:flex-row sm:space-y-0 sm:space-x-3">
                  <div className="flex flex-col space-y-2 sm:flex-row sm:space-y-0 sm:space-x-3">
                    <button
                      onClick={() => onStartChat(bot.id)}
                      disabled={status === 'checking'}
                      className={`
                        inline-flex items-center justify-center px-4 py-3 sm:px-6 sm:py-3 rounded-lg font-medium transition-colors touch-target w-full sm:w-auto text-sm sm:text-base
                        ${status === 'available'
                          ? 'bg-blue-600 text-white hover:bg-blue-700'
                          : status === 'checking'
                          ? 'bg-gray-400 text-white cursor-not-allowed'
                          : 'bg-yellow-600 text-white hover:bg-yellow-700'
                        }
                      `}
                    >
                      {status === 'checking' ? (
                        <>
                          <RefreshCw className="h-4 w-4 sm:h-5 sm:w-5 mr-2 animate-spin" />
                          Verificando...
                        </>
                      ) : status === 'available' ? (
                        <>
                          <Play className="h-4 w-4 sm:h-5 sm:w-5 mr-2" />
                          Iniciar Demo
                        </>
                      ) : (
                        <>
                          <MessageSquare className="h-4 w-4 sm:h-5 sm:w-5 mr-2" />
                          Probar de todos modos
                        </>
                      )}
                    </button>

                    {/* Botón Cargar Contacto - Solo para bot de cobranza */}
                    {bot.id === 'demos-bot-cobranza' && (
                      <>
                        <button
                          onClick={() => setIsContactModalOpen(true)}
                          className="inline-flex items-center justify-center px-4 py-3 sm:px-6 sm:py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors font-medium touch-target w-full sm:w-auto text-sm sm:text-base"
                        >
                          <UserPlus className="h-4 w-4 sm:h-5 sm:w-5 mr-2" />
                          Cargar Contacto
                        </button>

                        {/* Botón temporal de prueba de autenticación - Solo en desarrollo */}
                        {isLocalDevelopment && (
                          <button
                            onClick={handleTestAuth}
                            className="inline-flex items-center justify-center px-3 py-2 sm:px-4 sm:py-2 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700 transition-colors font-medium text-xs sm:text-sm touch-target w-full sm:w-auto"
                          >
                            🧪 Test Auth
                          </button>
                        )}
                      </>
                    )}
                  </div>

                  {status !== 'available' && status !== 'checking' && (
                    <p className="text-xs sm:text-sm text-white/80 text-center mt-2">
                      {status === 'error'
                        ? 'Puede usar chat interno como alternativa'
                        : 'Bot no configurado, usará chat interno'
                      }
                    </p>
                  )}
                </div>
              </div>
            </div>
          </div>

          <div className="p-4 sm:p-6 lg:p-8 space-y-6 sm:space-y-8">
            {/* Description */}
            <div>
              <h2 className="text-xl sm:text-2xl font-bold text-gray-900 mb-3 sm:mb-4">Descripción</h2>
              <p className="text-gray-600 text-base sm:text-lg leading-relaxed">{bot.fullDescription}</p>
            </div>

            {/* Contact Info */}
            <div className="p-4 sm:p-6 bg-gray-50 rounded-lg border border-gray-200">
              <h3 className="text-base sm:text-lg font-semibold text-gray-900 mb-3 sm:mb-4">Información de Contacto</h3>
              <div className="space-y-2 sm:space-y-3">
                {bot.endpoint && (
                  <div className="flex items-center text-gray-600">
                    <ExternalLink className="h-5 w-5 mr-3 text-blue-600" />
                    <span className="text-sm">Endpoint: {bot.endpoint}</span>
                  </div>
                )}

                {/* Números de teléfono internacionales */}
                <div className="space-y-2">
                  <h4 className="text-sm font-medium text-gray-700 mb-2">Teléfonos de Soporte:</h4>
                  {phoneNumbers.map((phone, index) => (
                    <div key={index} className="flex items-center text-gray-600">
                      <ReactCountryFlag
                        countryCode={phone.country}
                        svg
                        style={{
                          width: '1.25rem',
                          height: '1rem',
                          marginRight: '0.75rem'
                        }}
                        title={phone.countryName}
                      />
                      <span className="text-sm font-mono">{phone.number}</span>
                      <span className="text-xs text-gray-500 ml-2">({phone.countryName})</span>
                    </div>
                  ))}
                </div>

                {bot.contactNumber && (
                  <div className="flex items-center text-gray-600 pt-2 border-t border-gray-200">
                    <Phone className="h-5 w-5 mr-3 text-green-600" />
                    <span className="text-sm">Teléfono Local: {bot.contactNumber}</span>
                  </div>
                )}
              </div>
            </div>

            {/* Expected Conversation Flow */}
            <div>
              <div className="flex items-center mb-6">
                <MessageSquare className="h-6 w-6 text-blue-600 mr-3" />
                <h2 className="text-2xl font-bold text-gray-900">Flujo de conversación esperado</h2>
              </div>
              <p className="text-gray-600 mb-6">Este es el flujo de conversación esperado del bot:</p>
              
              <div className="overflow-x-auto">
                <table className="w-full border-collapse bg-white rounded-lg overflow-hidden shadow-sm">
                  <thead>
                    <tr className="bg-gray-50">
                      <th className="border border-gray-200 px-4 py-3 text-left font-semibold text-gray-900">
                        Respuesta del Bot
                      </th>
                      <th className="border border-gray-200 px-4 py-3 text-left font-semibold text-gray-900">
                        Entrada del Usuario
                      </th>
                      <th className="border border-gray-200 px-4 py-3 text-left font-semibold text-gray-900">
                        Resultado Esperado
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    {bot.conversationFlow.map((step, index) => (
                      <tr key={index} className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50/50'}>
                        <td className="border border-gray-200 px-4 py-3 text-sm text-blue-800">
                          {step.userInput}
                        </td>
                        <td className="border border-gray-200 px-4 py-3 text-sm text-gray-600">
                          {step.botResponse}
                        </td>
                        <td className="border border-gray-200 px-4 py-3 text-sm text-gray-600">
                          {step.expectedResult}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>

            {/* Alternative Cases / Variations */}
            {bot.id === 'demos-bot-agendamiento' && (
              <div>
                <div className="flex items-center mb-6">
                  <RefreshCw className="h-6 w-6 text-green-600 mr-3" />
                  <h3 className="text-xl font-bold text-gray-900">3. Casos alternativos / variaciones</h3>
                </div>
                <div className="space-y-4">
                  <div>
                    <h4 className="font-semibold text-gray-900 mb-2">• Agendar:</h4>
                    <div className="ml-4 space-y-1">
                      <p className="text-gray-700 text-sm">"Quiero agendar una cita médica con la dra. Loyola"</p>
                      <p className="text-gray-700 text-sm">"Me gustaría agendar una hora para mañana"</p>
                      <p className="text-gray-700 text-sm">"Quiero agendar una hora para medicina general"</p>
                    </div>
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-900 mb-2">• Doctores:</h4>
                    <div className="ml-4 space-y-1">
                      <p className="text-gray-700 text-sm">"Con el Doctor Jorge Lopez"</p>
                      <p className="text-gray-700 text-sm">"Con la dra. Loyola"</p>
                    </div>
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-900 mb-2">• Horarios:</h4>
                    <div className="ml-4 space-y-1">
                      <p className="text-gray-700 text-sm">"a las 9 am"</p>
                      <p className="text-gray-700 text-sm">"a las 10:30"</p>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Success Criteria */}
            <div>
              <div className="flex items-center mb-6">
                <CheckCircle className="h-6 w-6 text-green-600 mr-3" />
                <h3 className="text-xl font-bold text-gray-900">
                  3. Criterios de éxito
                </h3>
              </div>
              <ul className="space-y-3">
                {bot.id === 'demos-bot-agendamiento' ? (
                  <>
                    <li className="flex items-start">
                      <div className="h-2 w-2 bg-green-600 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                      <p className="text-gray-700">El bot detecta correctamente la intención de agendar.</p>
                    </li>
                    <li className="flex items-start">
                      <div className="h-2 w-2 bg-green-600 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                      <p className="text-gray-700">Solicita la especialidad, fecha y horario sin errores.</p>
                    </li>
                    <li className="flex items-start">
                      <div className="h-2 w-2 bg-green-600 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                      <p className="text-gray-700">Confirma la cita y permite enviar recordatorio a través de whatsapp.</p>
                    </li>
                    <li className="flex items-start">
                      <div className="h-2 w-2 bg-green-600 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                      <p className="text-gray-700">Finaliza de forma clara y cortés.</p>
                    </li>
                  </>
                ) : bot.id === 'demos-bot-cobranza' ? (
                  <>
                    <li className="flex items-start">
                      <div className="h-2 w-2 bg-green-600 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                      <p className="text-gray-700">El bot detecta correctamente la confirmación del usuario.</p>
                    </li>
                    <li className="flex items-start">
                      <div className="h-2 w-2 bg-green-600 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                      <p className="text-gray-700">Finaliza de forma clara y cortés.</p>
                    </li>
                  </>
                ) : (
                  <>
                    <li className="flex items-start">
                      <div className="h-2 w-2 bg-green-600 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                      <p className="text-gray-700">El bot responde correctamente a las dudas o consultas del usuario.</p>
                    </li>
                    <li className="flex items-start">
                      <div className="h-2 w-2 bg-green-600 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                      <p className="text-gray-700">Finaliza de forma clara y cortés.</p>
                    </li>
                  </>
                )}
              </ul>
            </div>

            {/* CTA */}
            <div className="text-center pt-8 border-t border-gray-200">
              <button
                onClick={() => onStartChat(bot.id)}
                className="inline-flex items-center px-8 py-4 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-semibold text-lg"
              >
                <Play className="h-6 w-6 mr-3" />
                Iniciar Demo Ahora
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Modal de Cargar Contacto */}
      <ContactFormModal
        isOpen={isContactModalOpen}
        onClose={() => setIsContactModalOpen(false)}
        onSubmit={handleContactSubmit}
      />
    </div>
  );
};

export default BotDetail;