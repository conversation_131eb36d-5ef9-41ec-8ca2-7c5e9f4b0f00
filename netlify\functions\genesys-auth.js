// Netlify Function para manejar autenticación con Genesys Cloud
exports.handler = async (event, context) => {
  // Solo permitir POST
  if (event.httpMethod !== 'POST') {
    return {
      statusCode: 405,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        'Access-Control-Allow-Methods': 'POST, OPTIONS'
      },
      body: JSON.stringify({ error: 'Method not allowed' })
    };
  }

  // Manejar preflight OPTIONS request
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        'Access-Control-Allow-Methods': 'POST, OPTIONS'
      },
      body: ''
    };
  }

  try {
    console.log('🔐 Solicitando token de Genesys Cloud...');
    
    // Credenciales OAuth (deberían estar en variables de entorno en producción)
    const CLIENT_CREDENTIALS = 'NzIwM2VjZmUtOGYwMC00ZmU3LThhNjQtMjY2NDRjMGY1NTdmOjhWa2FPS1FfZmtNMWNsT2I3YlpUaFNEUFJSb3c1WVE5aXhqTzE3Q0NOVW8=';
    
    const response = await fetch('https://login.mypurecloud.com/oauth/token?grant_type=client_credentials', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Authorization': `Basic ${CLIENT_CREDENTIALS}`
      }
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Error al obtener token:', {
        status: response.status,
        statusText: response.statusText,
        body: errorText
      });
      
      return {
        statusCode: response.status,
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          error: `Error ${response.status}: ${response.statusText}`,
          details: errorText
        })
      };
    }

    const tokenData = await response.json();
    console.log('✅ Token obtenido exitosamente');
    
    return {
      statusCode: 200,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(tokenData)
    };

  } catch (error) {
    console.error('💥 Error en función de autenticación:', error);
    
    return {
      statusCode: 500,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        error: 'Error interno del servidor',
        details: error.message
      })
    };
  }
};
