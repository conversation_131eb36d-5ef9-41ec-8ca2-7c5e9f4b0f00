import React from 'react';
import { Loader2, Bot } from 'lucide-react';

interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg';
  message?: string;
  fullScreen?: boolean;
  variant?: 'default' | 'minimal' | 'bot';
}

const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  size = 'md',
  message = 'Cargando...',
  fullScreen = false,
  variant = 'default'
}) => {
  const getSizeClasses = () => {
    switch (size) {
      case 'sm':
        return {
          spinner: 'h-4 w-4',
          text: 'text-sm',
          container: 'p-4'
        };
      case 'lg':
        return {
          spinner: 'h-12 w-12',
          text: 'text-lg',
          container: 'p-8'
        };
      default:
        return {
          spinner: 'h-8 w-8',
          text: 'text-base',
          container: 'p-6'
        };
    }
  };

  const sizeClasses = getSizeClasses();

  const renderSpinner = () => {
    switch (variant) {
      case 'minimal':
        return (
          <Loader2 className={`${sizeClasses.spinner} animate-spin text-blue-600`} />
        );
      
      case 'bot':
        return (
          <div className="relative">
            <Bot className={`${sizeClasses.spinner} text-blue-600 animate-pulse`} />
            <div className="absolute -bottom-1 -right-1">
              <Loader2 className="h-3 w-3 animate-spin text-blue-400" />
            </div>
          </div>
        );
      
      default:
        return (
          <div className="relative">
            <div className={`${sizeClasses.spinner} border-4 border-blue-200 border-t-blue-600 rounded-full animate-spin`}></div>
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="h-2 w-2 bg-blue-600 rounded-full animate-ping"></div>
            </div>
          </div>
        );
    }
  };

  const content = (
    <div className={`flex flex-col items-center justify-center ${sizeClasses.container}`}>
      {renderSpinner()}
      {message && (
        <p className={`mt-4 text-gray-600 ${sizeClasses.text} font-medium animate-pulse`}>
          {message}
        </p>
      )}
    </div>
  );

  if (fullScreen) {
    return (
      <div className="fixed inset-0 bg-white/80 backdrop-blur-sm flex items-center justify-center z-50">
        <div className="bg-white rounded-xl shadow-lg border border-gray-200/50">
          {content}
        </div>
      </div>
    );
  }

  return (
    <div className="flex items-center justify-center min-h-[200px]">
      {content}
    </div>
  );
};

export default LoadingSpinner;
