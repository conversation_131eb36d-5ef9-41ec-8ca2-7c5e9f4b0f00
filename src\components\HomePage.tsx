import React, { memo, useMemo } from 'react';
import { Bot } from '../types/bot';
import BotCard from './BotCard';
import StaggeredAnimation from './StaggeredAnimation';
import { Search, Sparkles } from 'lucide-react';

interface HomePageProps {
  bots: Bot[];
  searchTerm: string;
  onBotDetail: (botId: string) => void;
  onBotDemo: (botId: string) => void;
}

const HomePage: React.FC<HomePageProps> = ({ bots, searchTerm, onBotDetail, onBotDemo }) => {
  // Filtrar bots basado en el término de búsqueda
  const filteredBots = useMemo(() => {
    return bots.filter(bot =>
      bot.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      bot.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      bot.category.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }, [bots, searchTerm]);

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50/30">
      {/* Hero Section */}
      <div className="relative px-6 pt-8 pb-16 sm:px-8 lg:px-12">
        {/* Background decoration */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          <div className="absolute -top-40 -right-32 w-80 h-80 bg-blue-400/10 rounded-full blur-3xl"></div>
          <div className="absolute -bottom-40 -left-32 w-80 h-80 bg-indigo-400/10 rounded-full blur-3xl"></div>
        </div>

        <div className="relative max-w-6xl mx-auto">
          <div className="text-center">
            {/* Badge */}
            <div className="flex justify-center mb-8">
              <div className="inline-flex items-center px-4 py-2 rounded-full bg-gradient-to-r from-blue-100 to-indigo-100 text-blue-800 text-sm font-semibold shadow-sm border border-blue-200/50">
                <Sparkles className="h-4 w-4 mr-2 text-blue-600" />
                Inteligencia Artificial Conversacional
              </div>
            </div>

            {/* Main heading */}
            <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold text-gray-900 mb-6 leading-tight">
              Portal de Demostración
              <br />
              <span className="bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
                de Bots
              </span>
            </h1>

            {/* Subtitle */}
            <p className="text-lg sm:text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed mb-8">
              Descubre cómo nuestros bots conversacionales pueden transformar tu negocio con
              <span className="font-semibold text-gray-700"> interacciones inteligentes</span> y
              <span className="font-semibold text-gray-700"> automatización avanzada</span>.
            </p>

            {/* Stats or features */}
            <div className="flex flex-wrap justify-center gap-8 text-sm text-gray-600">
              <div className="flex items-center">
                <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                <span>Disponible 24/7</span>
              </div>
              <div className="flex items-center">
                <div className="w-2 h-2 bg-blue-500 rounded-full mr-2"></div>
                <span>Múltiples idiomas</span>
              </div>
              <div className="flex items-center">
                <div className="w-2 h-2 bg-purple-500 rounded-full mr-2"></div>
                <span>Integración fácil</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Search Results */}
      {searchTerm && (
        <div className="px-6 sm:px-8 lg:px-12 mb-8">
          <div className="max-w-6xl mx-auto">
            <div className="p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl border border-blue-200/50 shadow-sm">
              <div className="flex items-center">
                <Search className="h-5 w-5 text-blue-600 mr-3" />
                <p className="text-blue-800 font-medium">
                  {filteredBots.length === 0
                    ? `No se encontraron resultados para "${searchTerm}"`
                    : `${filteredBots.length} resultado${filteredBots.length !== 1 ? 's' : ''} encontrado${filteredBots.length !== 1 ? 's' : ''} para "${searchTerm}"`
                  }
                </p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Bots Section */}
      <div className="px-6 sm:px-8 lg:px-12 pb-16">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-4">
              Bots Disponibles
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Selecciona un bot para explorar sus capacidades y ver una demostración en vivo
            </p>
          </div>

          {filteredBots.length === 0 ? (
            <div className="text-center py-20">
              <div className="max-w-md mx-auto">
                <div className="bg-gradient-to-br from-gray-100 to-gray-200 rounded-full h-24 w-24 mx-auto mb-6 flex items-center justify-center shadow-sm">
                  <Search className="h-12 w-12 text-gray-500" />
                </div>
                <h3 className="text-2xl font-bold text-gray-900 mb-4">No se encontraron bots</h3>
                <p className="text-gray-600 mb-8 leading-relaxed">
                  No encontramos bots que coincidan con tu búsqueda. Intenta con otros términos o explora todos nuestros bots disponibles.
                </p>
                <button
                  onClick={() => window.location.reload()}
                  className="inline-flex items-center px-8 py-3 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-xl hover:from-blue-700 hover:to-indigo-700 transition-all duration-200 font-semibold shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
                >
                  Ver todos los bots
                </button>
              </div>
            </div>
          ) : (
            <StaggeredAnimation
              staggerDelay={150}
              initialDelay={200}
              animationType="fade-up"
              className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-8"
            >
              {filteredBots.map((bot) => (
                <BotCard
                  key={bot.id}
                  bot={bot}
                  onViewDetails={() => onBotDetail(bot.id)}
                  onTryBot={() => onBotDemo(bot.id)}
                />
              ))}
            </StaggeredAnimation>
          )}
        </div>
      </div>
    </div>
  );
};

export default memo(HomePage);