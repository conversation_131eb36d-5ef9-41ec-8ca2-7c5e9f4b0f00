import React, { memo } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON> as Bot<PERSON><PERSON>, CheckCircle, Clock, AlertCircle } from 'lucide-react';
import { Bot, Message } from '../types/bot';

interface EnhancedMessageProps {
  message: Message;
  bot: Bot;
  isTyping?: boolean;
  showMetadata?: boolean;
}

const EnhancedMessage: React.FC<EnhancedMessageProps> = ({
  message,
  bot,
  isTyping = false,
  showMetadata = false
}) => {
  const formatMessageContent = (content: string) => {
    // Detectar y formatear diferentes tipos de contenido
    const lines = content.split('\n');
    
    return lines.map((line, index) => {
      // Detectar listas
      if (line.trim().startsWith('•') || line.trim().startsWith('-')) {
        return (
          <li key={index} className="ml-4 mb-1">
            {line.replace(/^[•-]\s*/, '')}
          </li>
        );
      }
      
      // Detectar texto en negrita (entre **)
      if (line.includes('**')) {
        const parts = line.split('**');
        return (
          <p key={index} className="mb-2">
            {parts.map((part, partIndex) => 
              partIndex % 2 === 1 ? (
                <strong key={partIndex} className="font-semibold">{part}</strong>
              ) : (
                part
              )
            )}
          </p>
        );
      }
      
      // Detectar enlaces
      const urlRegex = /(https?:\/\/[^\s]+)/g;
      if (urlRegex.test(line)) {
        const parts = line.split(urlRegex);
        return (
          <p key={index} className="mb-2">
            {parts.map((part, partIndex) => 
              urlRegex.test(part) ? (
                <a 
                  key={partIndex} 
                  href={part} 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="text-blue-600 hover:text-blue-800 underline"
                >
                  {part}
                </a>
              ) : (
                part
              )
            )}
          </p>
        );
      }
      
      // Línea normal
      return line.trim() ? (
        <p key={index} className="mb-2 last:mb-0">
          {line}
        </p>
      ) : (
        <br key={index} />
      );
    });
  };

  const getMessageStatus = () => {
    if (isTyping) {
      return <Clock className="w-3 h-3 text-gray-400" />;
    }
    return <CheckCircle className="w-3 h-3 text-green-500" />;
  };

  return (
    <div className={`flex ${message.sender === 'user' ? 'justify-end' : 'justify-start'}`}>
      <div className={`flex max-w-md lg:max-w-2xl ${message.sender === 'user' ? 'flex-row-reverse' : 'flex-row'}`}>
        {/* Avatar */}
        <div className="flex-shrink-0">
          {message.sender === 'bot' ? (
            <div className="h-10 w-10 rounded-full overflow-hidden bg-gray-100 shadow-sm ring-2 ring-white">
              <img 
                src={bot.imageUrl} 
                alt={bot.name}
                className="w-full h-full object-cover"
              />
            </div>
          ) : (
            <div className="h-10 w-10 rounded-full bg-gradient-to-br from-blue-600 to-indigo-600 flex items-center justify-center shadow-sm ring-2 ring-white">
              <User className="h-5 w-5 text-white" />
            </div>
          )}
        </div>

        {/* Message Content */}
        <div className={`mx-4 ${message.sender === 'user' ? 'order-first' : ''}`}>
          <div
            className={`px-4 py-3 rounded-2xl shadow-sm relative ${
              message.sender === 'user'
                ? 'bg-gradient-to-r from-blue-600 to-indigo-600 text-white'
                : 'bg-white/90 backdrop-blur-sm text-gray-800 border border-gray-200/50'
            }`}
          >
            {/* Typing indicator */}
            {isTyping && message.sender === 'bot' ? (
              <div className="flex items-center space-x-1">
                <div className="flex space-x-1">
                  <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                  <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                  <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                </div>
                <span className="text-sm text-gray-500 ml-2">Escribiendo...</span>
              </div>
            ) : (
              <div className="text-sm leading-relaxed">
                {formatMessageContent(message.content)}
              </div>
            )}

            {/* Message tail */}
            <div
              className={`absolute top-3 w-3 h-3 transform rotate-45 ${
                message.sender === 'user'
                  ? 'right-[-6px] bg-gradient-to-br from-blue-600 to-indigo-600'
                  : 'left-[-6px] bg-white border-l border-t border-gray-200/50'
              }`}
            />
          </div>

          {/* Message metadata */}
          <div className={`flex items-center mt-2 px-2 ${message.sender === 'user' ? 'justify-end' : 'justify-start'}`}>
            <div className="flex items-center space-x-2">
              <span className="text-xs text-gray-500">
                {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
              </span>
              {message.sender === 'user' && getMessageStatus()}
            </div>
          </div>

          {/* Bot metadata (confidence, intent, etc.) */}
          {showMetadata && message.sender === 'bot' && (
            <div className="mt-2 px-2">
              <div className="flex items-center space-x-2 text-xs text-gray-400">
                <BotIcon className="w-3 h-3" />
                <span>Respuesta automática</span>
                <span>•</span>
                <span>Confianza: 85%</span>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default memo(EnhancedMessage);
