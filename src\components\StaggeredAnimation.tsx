import React, { useEffect, useState } from 'react';

interface StaggeredAnimationProps {
  children: React.ReactNode[];
  staggerDelay?: number;
  initialDelay?: number;
  animationType?: 'fade-up' | 'fade-in' | 'slide-up' | 'scale';
  duration?: number;
  className?: string;
}

const StaggeredAnimation: React.FC<StaggeredAnimationProps> = ({
  children,
  staggerDelay = 100,
  initialDelay = 0,
  animationType = 'fade-up',
  duration = 300,
  className = ''
}) => {
  const [visibleItems, setVisibleItems] = useState<boolean[]>(
    new Array(children.length).fill(false)
  );

  useEffect(() => {
    const timeouts: NodeJS.Timeout[] = [];

    children.forEach((_, index) => {
      const delay = initialDelay + (index * staggerDelay);
      
      const timeout = setTimeout(() => {
        setVisibleItems(prev => {
          const newState = [...prev];
          newState[index] = true;
          return newState;
        });
      }, delay);

      timeouts.push(timeout);
    });

    return () => {
      timeouts.forEach(timeout => clearTimeout(timeout));
    };
  }, [children.length, staggerDelay, initialDelay]);

  const getAnimationClasses = (isVisible: boolean) => {
    const baseClasses = `transition-all transform`;
    const durationClass = `duration-${duration}`;
    
    switch (animationType) {
      case 'fade-up':
        return `${baseClasses} ${durationClass} ${
          isVisible 
            ? 'opacity-100 translate-y-0' 
            : 'opacity-0 translate-y-4'
        }`;
      case 'fade-in':
        return `${baseClasses} ${durationClass} ${
          isVisible 
            ? 'opacity-100' 
            : 'opacity-0'
        }`;
      case 'slide-up':
        return `${baseClasses} ${durationClass} ${
          isVisible 
            ? 'translate-y-0' 
            : 'translate-y-8'
        }`;
      case 'scale':
        return `${baseClasses} ${durationClass} ${
          isVisible 
            ? 'opacity-100 scale-100' 
            : 'opacity-0 scale-95'
        }`;
      default:
        return `${baseClasses} ${durationClass} ${
          isVisible 
            ? 'opacity-100 translate-y-0' 
            : 'opacity-0 translate-y-4'
        }`;
    }
  };

  return (
    <div className={className}>
      {children.map((child, index) => (
        <div
          key={index}
          className={getAnimationClasses(visibleItems[index])}
          style={{
            transitionTimingFunction: 'cubic-bezier(0.4, 0, 0.2, 1)'
          }}
        >
          {child}
        </div>
      ))}
    </div>
  );
};

export default StaggeredAnimation;
