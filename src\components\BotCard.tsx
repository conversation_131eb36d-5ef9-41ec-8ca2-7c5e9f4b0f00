import React, { memo } from 'react';
import { Bot } from '../types/bot';
import { ExternalLink, Play } from 'lucide-react';
import { useBotAvailability } from '../hooks/useBotAvailability';
import BotStatusIndicator from './BotStatusIndicator';

interface BotCardProps {
  bot: Bot;
  onViewDetails: () => void;
  onTryBot: () => void;
}

const BotCard: React.FC<BotCardProps> = ({ bot, onViewDetails, onTryBot }) => {
  const { status, message, retry } = useBotAvailability(bot.id);

  return (
    <div className="group bg-white rounded-lg shadow border border-gray-200 hover:shadow-lg transition-all duration-300 overflow-hidden transform hover:scale-[1.02] hover:-translate-y-1">
      <div className="relative">
        <div className="h-36 sm:h-48 overflow-hidden">
          <img 
            src={bot.imageUrl} 
            alt={bot.name}
            className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
          />
        </div>
        <div className="absolute top-2 right-2 sm:top-4 sm:right-4 flex items-center space-x-2">
          <span className="inline-flex items-center px-2 py-0.5 sm:px-3 sm:py-1 rounded-full text-xs font-medium bg-white text-gray-800 border border-gray-200">
            {bot.category}
          </span>
        </div>
        <div className="absolute top-2 left-2 sm:top-4 sm:left-4">
          <BotStatusIndicator
            status={status}
            message={message}
            size="sm"
            onRetry={retry}
          />
        </div>
      </div>
      
      <div className="p-4 sm:p-6 space-y-3 sm:space-y-4">
        <div>
          <h3 className="text-lg sm:text-xl font-bold text-gray-900 mb-1.5 sm:mb-2">{bot.name}</h3>
          <p className="text-gray-600 text-xs sm:text-sm leading-relaxed line-clamp-3 mb-1.5 sm:mb-2">
            {bot.description}
          </p>
          {status !== 'available' && status !== 'checking' && (
            <div className="mt-2">
              <BotStatusIndicator
                status={status}
                message={message}
                showText={true}
                size="sm"
                onRetry={retry}
              />
            </div>
          )}
        </div>
        
        <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-3 pt-1.5 sm:pt-2">
          <button
            onClick={onViewDetails}
            className="flex-1 flex items-center justify-center px-3 py-2 sm:px-4 sm:py-2.5 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 hover:border-gray-400 transition-all duration-200 text-xs sm:text-sm font-medium transform hover:scale-105"
          >
            <ExternalLink className="h-3 w-3 sm:h-4 sm:w-4 mr-1.5 sm:mr-2 transition-transform duration-200 group-hover:rotate-12" />
            Ver Detalles
          </button>
          <button
            onClick={onTryBot}
            className="flex-1 flex items-center justify-center px-3 py-2 sm:px-4 sm:py-2.5 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-all duration-200 text-xs sm:text-sm font-medium transform hover:scale-105 hover:shadow-lg"
          >
            <Play className="h-3 w-3 sm:h-4 sm:w-4 mr-1.5 sm:mr-2 transition-transform duration-200 group-hover:translate-x-1" />
            Probar Bot
          </button>
        </div>
      </div>
    </div>
  );
};

export default memo(BotCard);