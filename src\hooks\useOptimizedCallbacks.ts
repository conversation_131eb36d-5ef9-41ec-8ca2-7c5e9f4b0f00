import { useCallback, useMemo } from 'react';

/**
 * Hook para optimizar callbacks y evitar re-renders innecesarios
 * Proporciona versiones memoizadas de funciones comunes
 */
export const useOptimizedCallbacks = () => {
  // Callback optimizado para manejar clicks de botones
  const handleButtonClick = useCallback((callback: () => void) => {
    return (event: React.MouseEvent) => {
      event.preventDefault();
      event.stopPropagation();
      callback();
    };
  }, []);

  // Callback optimizado para manejar cambios de input
  const handleInputChange = useCallback((callback: (value: string) => void) => {
    return (event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
      callback(event.target.value);
    };
  }, []);

  // Callback optimizado para manejar teclas presionadas
  const handleKeyPress = useCallback((callback: () => void, key: string = 'Enter') => {
    return (event: React.KeyboardEvent) => {
      if (event.key === key && !event.shiftKey) {
        event.preventDefault();
        callback();
      }
    };
  }, []);

  // Callback optimizado para manejar selección de elementos
  const handleItemSelect = useCallback((callback: (id: string) => void) => {
    return (id: string) => {
      callback(id);
    };
  }, []);

  // Función para crear callbacks memoizados con parámetros
  const createMemoizedCallback = useCallback(<T extends any[]>(
    callback: (...args: T) => void,
    deps: any[] = []
  ) => {
    // eslint-disable-next-line react-hooks/exhaustive-deps
    return useCallback(callback, deps);
  }, []);

  return {
    handleButtonClick,
    handleInputChange,
    handleKeyPress,
    handleItemSelect,
    createMemoizedCallback
  };
};

/**
 * Hook para memoizar valores computados pesados
 */
export const useOptimizedMemo = () => {
  // Memoizar filtros de arrays
  const memoizeFilter = useCallback(<T>(
    array: T[],
    filterFn: (item: T) => boolean,
    deps: any[] = []
  ) => {
    // eslint-disable-next-line react-hooks/exhaustive-deps
    return useMemo(() => array.filter(filterFn), [array, ...deps]);
  }, []);

  // Memoizar mapeos de arrays
  const memoizeMap = useCallback(<T, R>(
    array: T[],
    mapFn: (item: T) => R,
    deps: any[] = []
  ) => {
    // eslint-disable-next-line react-hooks/exhaustive-deps
    return useMemo(() => array.map(mapFn), [array, ...deps]);
  }, []);

  // Memoizar ordenamientos
  const memoizeSort = useCallback(<T>(
    array: T[],
    sortFn: (a: T, b: T) => number,
    deps: any[] = []
  ) => {
    // eslint-disable-next-line react-hooks/exhaustive-deps
    return useMemo(() => [...array].sort(sortFn), [array, ...deps]);
  }, []);

  // Memoizar agrupaciones
  const memoizeGroupBy = useCallback(<T, K extends string | number>(
    array: T[],
    keyFn: (item: T) => K,
    deps: any[] = []
  ) => {
    // eslint-disable-next-line react-hooks/exhaustive-deps
    return useMemo(() => {
      return array.reduce((groups, item) => {
        const key = keyFn(item);
        if (!groups[key]) {
          groups[key] = [];
        }
        groups[key].push(item);
        return groups;
      }, {} as Record<K, T[]>);
    }, [array, ...deps]);
  }, []);

  return {
    memoizeFilter,
    memoizeMap,
    memoizeSort,
    memoizeGroupBy
  };
};
