# 🚀 Deployment Instructions - Portal Demo Bots Crossnet

## 📋 Resumen de la Solución CORS

Se implementó una solución usando **Netlify Functions** (serverless) para manejar las llamadas a la API de Genesys Cloud y evitar problemas de CORS.

## 🏗️ Arquitectura

```
Frontend (React) → Netlify Functions → Genesys Cloud API
```

### Funciones Serverless:
- `/.netlify/functions/genesys-auth` - Obtiene tokens OAuth
- `/.netlify/functions/genesys-contacts` - Crea contactos en Genesys Cloud

## 🔧 Archivos Agregados/Modificados

### Nuevos Archivos:
- `netlify/functions/genesys-auth.js` - Función para autenticación OAuth
- `netlify/functions/genesys-contacts.js` - Función para crear contactos
- `netlify.toml` - Configuración de Netlify
- `DEPLOYMENT.md` - Este archivo

### Archivos Modificados:
- `src/services/genesysCloudApi.ts` - Actualizado para usar Netlify Functions
- `package.json` - Agregado Netlify CLI

## 🚀 Deployment en Netlify

### Opción 1: Deployment Automático (Recomendado)

1. **Conecta tu repositorio** a Netlify:
   - Ve a [netlify.com](https://netlify.com)
   - "New site from Git"
   - Conecta tu repositorio de GitHub

2. **Configuración automática**:
   - Netlify detectará automáticamente `netlify.toml`
   - Build command: `npm run build`
   - Publish directory: `dist`

3. **Deploy**:
   - Netlify construirá y desplegará automáticamente
   - Las funciones serverless se desplegarán automáticamente

### Opción 2: Deployment Manual

```bash
# 1. Instalar Netlify CLI globalmente
npm install -g netlify-cli

# 2. Login a Netlify
netlify login

# 3. Build del proyecto
npm run build

# 4. Deploy
netlify deploy --prod --dir=dist
```

## 🧪 Testing Local con Netlify Functions

Para probar las funciones serverless localmente:

```bash
# 1. Instalar dependencias (ya hecho)
npm install

# 2. Iniciar servidor de desarrollo con Netlify
npm run dev:netlify
```

Esto iniciará:
- Frontend en `http://localhost:8888`
- Funciones serverless en `http://localhost:8888/.netlify/functions/`

## 🔐 Variables de Entorno (Opcional)

Para mayor seguridad en producción, puedes mover las credenciales a variables de entorno:

### En Netlify Dashboard:
1. Ve a "Site settings" → "Environment variables"
2. Agrega:
   - `GENESYS_CLIENT_CREDENTIALS` = `NzIwM2VjZmUtOGYwMC00ZmU3LThhNjQtMjY2NDRjMGY1NTdmOjhWa2FPS1FfZmtNMWNsT2I3YlpUaFNEUFJSb3c1WVE5aXhqTzE3Q0NOVW8=`

### Actualizar función:
```javascript
// En netlify/functions/genesys-auth.js
const CLIENT_CREDENTIALS = process.env.GENESYS_CLIENT_CREDENTIALS || 'fallback_value';
```

## ✅ Verificación del Deployment

Después del deployment:

1. **Verifica que la aplicación carga** en la URL de Netlify
2. **Ve al Bot de Cobranza**
3. **Haz clic en "🧪 Test Auth"** para probar la autenticación
4. **Prueba el formulario "Cargar Contacto"**

### URLs de las funciones en producción:
- `https://tu-sitio.netlify.app/.netlify/functions/genesys-auth`
- `https://tu-sitio.netlify.app/.netlify/functions/genesys-contacts`

## 🐛 Troubleshooting

### Error: "Function not found"
- Verifica que `netlify.toml` esté en la raíz del proyecto
- Asegúrate de que las funciones estén en `netlify/functions/`

### Error: "CORS still blocked"
- Verifica que las funciones incluyan headers CORS
- Revisa los logs de Netlify Functions

### Error de autenticación:
- Verifica que las credenciales OAuth sean correctas
- Revisa los logs de la función en Netlify Dashboard

## 📊 Logs y Monitoreo

Para ver logs de las funciones:
1. Ve a Netlify Dashboard
2. "Functions" tab
3. Haz clic en la función específica
4. Ve los logs en tiempo real

## 🔄 Actualizaciones Futuras

Para actualizar el código:
1. Haz push a tu repositorio
2. Netlify redesplegará automáticamente
3. Las funciones se actualizarán automáticamente

¡La solución está lista para producción! 🎉
