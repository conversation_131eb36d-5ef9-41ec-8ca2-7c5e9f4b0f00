import React from 'react';
import { Bot } from '../types/bot';
import { X, FileText } from 'lucide-react';
import { useNavigate, useLocation } from 'react-router-dom';

interface SidebarProps {
  bots: Bot[];
  selectedBotId: string | null;
  onBotSelect: (botId: string) => void;
  isOpen: boolean;
  onClose: () => void;
}

const Sidebar: React.FC<SidebarProps> = ({ bots, selectedBotId, onBotSelect, isOpen, onClose }) => {
  const categories = Array.from(new Set(bots.map(bot => bot.category)));
  const navigate = useNavigate();
  const location = useLocation();

  return (
    <>
      {/* Mobile overlay with blur effect */}
      {isOpen && (
        <div className="fixed inset-0 z-40 lg:hidden">
          <div className="fixed inset-0 bg-gray-900/20 backdrop-blur-sm" onClick={onClose}></div>
        </div>
      )}

      {/* Sidebar */}
      <div className={`fixed top-16 left-0 bottom-0 z-50 w-80 bg-white/95 backdrop-blur-xl border-r border-gray-200/50 shadow-xl transform ${isOpen ? 'translate-x-0' : '-translate-x-full'} lg:translate-x-0 transition-transform duration-300 ease-in-out lg:block`}>
        <div className="flex items-center justify-between h-12 px-6 border-b border-gray-200/50 lg:hidden">
          <h2 className="text-lg font-semibold text-gray-900">Bots</h2>
          <button
            onClick={onClose}
            className="p-2 rounded-lg text-gray-400 hover:text-gray-600 hover:bg-gray-100/50 transition-all duration-200"
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        <div className="flex flex-col h-full overflow-hidden">
          <div className="px-6 py-6 border-b border-gray-200/50 hidden lg:block">
            <h2 className="text-lg font-bold text-gray-900">Bots Disponibles</h2>
            <p className="text-sm text-gray-600 mt-1">Selecciona un bot para ver detalles</p>
          </div>

          <div className="flex-1 overflow-y-auto px-6 py-4">
            {/* Documentation Section */}
            <div className="space-y-6 mb-8">
              <h3 className="text-sm font-semibold text-gray-500 uppercase tracking-wide mb-3">
                Documentación
              </h3>
              <div className="space-y-2">
                <button
                  onClick={() => {
                    navigate('/genesys-cloud-docs');
                    onClose();
                  }}
                  className={`w-full text-left p-3 rounded-xl transition-all duration-200 transform hover:scale-[1.02] ${
                    location.pathname === '/genesys-cloud-docs'
                      ? 'bg-blue-50/80 backdrop-blur-sm text-blue-700 border border-blue-200/50 shadow-md'
                      : 'text-gray-700 hover:bg-gray-50/80 backdrop-blur-sm border border-transparent hover:shadow-sm'
                  }`}
                >
                  <div className="flex items-center">
                    <div className="h-10 w-10 rounded-xl bg-blue-100 flex-shrink-0 shadow-sm transition-transform duration-200 hover:scale-110 flex items-center justify-center">
                      <FileText className="h-5 w-5 text-blue-600" />
                    </div>
                    <div className="ml-3 min-w-0 flex-1">
                      <p className="text-sm font-medium truncate">Habilitación Demos en Genesys Cloud</p>
                      <p className="text-xs text-gray-500 truncate">Configuración y setup</p>
                    </div>
                  </div>
                </button>
              </div>
            </div>

            {/* Bots Section */}
            {categories.map(category => {
              const categoryBots = bots.filter(bot => bot.category === category);
              return (
                <div key={category} className="space-y-6">
                  <h3 className="text-sm font-semibold text-gray-500 uppercase tracking-wide mb-3">
                    {category}
                  </h3>
                  <div className="space-y-2">
                    {categoryBots.map(bot => (
                      <button
                        key={bot.id}
                        onClick={() => onBotSelect(bot.id)}
                        className={`w-full text-left p-3 rounded-xl transition-all duration-200 transform hover:scale-[1.02] ${
                          selectedBotId === bot.id
                            ? 'bg-blue-50/80 backdrop-blur-sm text-blue-700 border border-blue-200/50 shadow-md'
                            : 'text-gray-700 hover:bg-gray-50/80 backdrop-blur-sm border border-transparent hover:shadow-sm'
                        }`}
                      >
                        <div className="flex items-center">
                          <div className="h-10 w-10 rounded-xl overflow-hidden bg-gray-100 flex-shrink-0 shadow-sm transition-transform duration-200 hover:scale-110">
                            <img
                              src={bot.imageUrl}
                              alt={bot.name}
                              className="w-full h-full object-cover"
                            />
                          </div>
                          <div className="ml-3 min-w-0 flex-1">
                            <p className="text-sm font-medium truncate">{bot.name}</p>
                            <p className="text-xs text-gray-500 truncate">{bot.category}</p>
                          </div>
                        </div>
                      </button>
                    ))}
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </div>
    </>
  );
};

export default Sidebar;