import React from 'react';
import { 
  <PERSON><PERSON>, 
  MessageCircle, 
  RefreshCw, 
  ArrowLeft, 
  AlertTriangle,
  <PERSON>ifi,
  <PERSON><PERSON><PERSON>,
  Clock
} from 'lucide-react';
import { Bot as BotType } from '../types/bot';
import { BotStatus } from '../hooks/useBotAvailability';

interface BotUnavailableScreenProps {
  bot: BotType;
  status: BotStatus;
  message?: string;
  onBack: () => void;
  onRetry?: () => void;
  onUseFallback?: () => void;
  canUseFallback?: boolean;
}

const BotUnavailableScreen: React.FC<BotUnavailableScreenProps> = ({
  bot,
  status,
  message,
  onBack,
  onRetry,
  onUseFallback,
  canUseFallback = true
}) => {
  const getStatusConfig = () => {
    switch (status) {
      case 'unavailable':
        return {
          icon: Settings,
          title: 'Bot no disponible',
          description: 'Este bot no está configurado actualmente',
          color: 'text-gray-500',
          bgColor: 'bg-gray-100',
          suggestions: [
            'Puedes probar con el chat interno como alternativa',
            'Contacta al administrador para configurar este bot',
            'Revisa otros bots disponibles en el menú lateral'
          ]
        };
      case 'error':
        return {
          icon: Wifi,
          title: 'Error de conexión',
          description: 'No se pudo conectar con el servicio del bot',
          color: 'text-red-500',
          bgColor: 'bg-red-100',
          suggestions: [
            'Verifica tu conexión a internet',
            'Intenta nuevamente en unos momentos',
            'Usa el chat interno mientras se resuelve el problema'
          ]
        };
      case 'maintenance':
        return {
          icon: Clock,
          title: 'Mantenimiento programado',
          description: 'El bot está temporalmente fuera de servicio',
          color: 'text-yellow-500',
          bgColor: 'bg-yellow-100',
          suggestions: [
            'El servicio se restablecerá pronto',
            'Puedes usar el chat interno como alternativa',
            'Intenta nuevamente en unos minutos'
          ]
        };
      default:
        return {
          icon: AlertTriangle,
          title: 'Servicio no disponible',
          description: 'El bot no está disponible en este momento',
          color: 'text-gray-500',
          bgColor: 'bg-gray-100',
          suggestions: [
            'Intenta nuevamente más tarde',
            'Usa el chat interno como alternativa'
          ]
        };
    }
  };

  const config = getStatusConfig();
  const Icon = config.icon;

  return (
    <div className="h-screen flex items-center justify-center bg-gray-50">
      <div className="max-w-md w-full mx-4">
        <div className="bg-white rounded-xl shadow-lg p-8 text-center">
          {/* Bot Avatar */}
          <div className="relative mb-6">
            <div className="w-20 h-20 mx-auto rounded-full overflow-hidden bg-gray-100">
              <img 
                src={bot.imageUrl} 
                alt={bot.name}
                className="w-full h-full object-cover opacity-50"
              />
            </div>
            <div className={`absolute -bottom-1 -right-1 w-8 h-8 ${config.bgColor} rounded-full flex items-center justify-center border-2 border-white`}>
              <Icon className={`w-4 h-4 ${config.color}`} />
            </div>
          </div>

          {/* Status Info */}
          <h2 className="text-xl font-bold text-gray-900 mb-2">
            {config.title}
          </h2>
          
          <p className="text-gray-600 mb-2">
            {bot.name}
          </p>
          
          <p className="text-gray-500 text-sm mb-6">
            {message || config.description}
          </p>

          {/* Suggestions */}
          <div className="bg-gray-50 rounded-lg p-4 mb-6 text-left">
            <h3 className="font-medium text-gray-900 mb-3 text-center">
              ¿Qué puedes hacer?
            </h3>
            <ul className="space-y-2">
              {config.suggestions.map((suggestion, index) => (
                <li key={index} className="flex items-start text-sm text-gray-600">
                  <div className="w-1.5 h-1.5 bg-blue-500 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                  {suggestion}
                </li>
              ))}
            </ul>
          </div>

          {/* Action Buttons */}
          <div className="space-y-3">
            {/* Retry Button */}
            {onRetry && (status === 'error' || status === 'maintenance') && (
              <button
                onClick={onRetry}
                className="w-full flex items-center justify-center px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium"
              >
                <RefreshCw className="w-4 h-4 mr-2" />
                Reintentar conexión
              </button>
            )}

            {/* Fallback to Internal Chat */}
            {canUseFallback && onUseFallback && (
              <button
                onClick={onUseFallback}
                className="w-full flex items-center justify-center px-4 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors font-medium"
              >
                <MessageCircle className="w-4 h-4 mr-2" />
                Usar chat interno
              </button>
            )}

            {/* Back Button */}
            <button
              onClick={onBack}
              className="w-full flex items-center justify-center px-4 py-3 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors font-medium"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Volver al inicio
            </button>
          </div>

          {/* Additional Info */}
          <div className="mt-6 pt-4 border-t border-gray-100">
            <p className="text-xs text-gray-400">
              Si el problema persiste, contacta al soporte técnico
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BotUnavailableScreen;
