import React from 'react';
import { ArrowLeft, Phone, Users, MessageSquare, Settings, ExternalLink } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

const GenesysCloudDocs: React.FC = () => {
  const navigate = useNavigate();

  const handleBack = () => {
    navigate('/');
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50/30">
      {/* Header */}
      <div className="bg-white/80 backdrop-blur-xl border-b border-gray-200/50 px-4 sm:px-6 lg:px-8 py-6 shadow-sm">
        <div className="max-w-6xl mx-auto">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <button
                onClick={handleBack}
                className="flex items-center text-blue-600 hover:text-blue-700 font-medium mr-6 transition-colors duration-200 group"
              >
                <ArrowLeft className="h-4 w-4 mr-2 group-hover:-translate-x-1 transition-transform" />
                Volver
              </button>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Habilitación Demos en Genesys Cloud</h1>
                <p className="text-gray-600 mt-1">Configuración completa para demos de bots conversacionales</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Introduction */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200/50 p-6 mb-8">
          <div className="flex items-start">
            <div className="flex-shrink-0">
              <div className="h-12 w-12 bg-blue-100 rounded-xl flex items-center justify-center">
                <Settings className="h-6 w-6 text-blue-600" />
              </div>
            </div>
            <div className="ml-4">
              <h2 className="text-lg font-semibold text-gray-900 mb-2">Objetivo</h2>
              <p className="text-gray-600 leading-relaxed">
                Este documento tiene como objetivo detallar los pasos necesarios para configurar el enrutamiento hacia los flujos de voz, 
                activar la campaña de cobranza y configurar un número para la sección digital en Genesys Cloud.
              </p>
            </div>
          </div>
        </div>

        {/* Section 1: Call Routing */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200/50 p-6 mb-8">
          <div className="flex items-center mb-6">
            <div className="h-10 w-10 bg-green-100 rounded-lg flex items-center justify-center mr-4">
              <Phone className="h-5 w-5 text-green-600" />
            </div>
            <div>
              <h2 className="text-xl font-bold text-gray-900">Enrutamiento de un número hacia los flujos de voz</h2>
              <p className="text-sm text-gray-500 mt-1">Ruta: Admin &gt; Call Routing</p>
            </div>
          </div>

          <div className="space-y-6">
            {/* Step 1 */}
            <div className="border-l-4 border-blue-500 pl-4">
              <h3 className="font-semibold text-gray-900 mb-2">1. Acceder a Call Routing</h3>
              <p className="text-gray-600 mb-4">
                En la sección Call Routing (Enrutamiento de llamada), busca por ejemplo el flujo "Demo Atención General" y verifica qué número tiene asignado.
              </p>
              <div className="bg-gray-50 rounded-lg p-4">
                <img 
                  src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAwIiBoZWlnaHQ9IjMwMCIgdmlld0JveD0iMCAwIDYwMCAzMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSI2MDAiIGhlaWdodD0iMzAwIiBmaWxsPSIjRjlGQUZCIi8+CjxyZWN0IHg9IjIwIiB5PSIyMCIgd2lkdGg9IjU2MCIgaGVpZ2h0PSI2MCIgZmlsbD0iIzM3NDE1MSIvPgo8dGV4dCB4PSI0MCIgeT0iNTUiIGZpbGw9IndoaXRlIiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMTgiIGZvbnQtd2VpZ2h0PSJib2xkIj5HZW5lc3lzIENsb3VkIEFkbWluPC90ZXh0Pgo8cmVjdCB4PSI0MCIgeT0iMTAwIiB3aWR0aD0iMTIwIiBoZWlnaHQ9IjQwIiBmaWxsPSIjMzMzOEZGIiByeD0iNCIvPgo8dGV4dCB4PSI2MCIgeT0iMTI1IiBmaWxsPSJ3aGl0ZSIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjE0Ij5DYWxsIFJvdXRpbmc8L3RleHQ+CjxyZWN0IHg9IjIwMCIgeT0iMTAwIiB3aWR0aD0iMTIwIiBoZWlnaHQ9IjQwIiBmaWxsPSIjRjNGNEY2IiByeD0iNCIvPgo8dGV4dCB4PSIyMjAiIHk9IjEyNSIgZmlsbD0iIzM3NDE1MSIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjE0Ij5PdXRib3VuZDwvdGV4dD4KPC9zdmc+" 
                  alt="Genesys Cloud Admin Interface" 
                  className="w-full rounded border"
                />
              </div>
            </div>

            {/* Step 2 */}
            <div className="border-l-4 border-blue-500 pl-4">
              <h3 className="font-semibold text-gray-900 mb-2">2. Editar configuración</h3>
              <p className="text-gray-600 mb-2">
                Si no ves el número que necesitas según el país, haz clic en los tres puntos (···) y selecciona Editar.
              </p>
              <p className="text-gray-600 mb-4">
                En la opción Inbound Numbers, busca e ingresa el número que deseas asignar.
              </p>
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <p className="text-blue-800 font-medium">Ejemplo: +56 2 2914 0364</p>
              </div>
            </div>

            {/* Important Note */}
            <div className="bg-amber-50 border border-amber-200 rounded-lg p-4">
              <h4 className="font-semibold text-amber-800 mb-2">⚠️ Nota Importante</h4>
              <p className="text-amber-700 mb-2">
                Si el número aparece con un ícono de robot, significa que ya está enrutado a otro flujo. Para liberarlo:
              </p>
              <ul className="list-disc list-inside text-amber-700 space-y-1 ml-4">
                <li>Verifica que no se esté utilizando en otra demo</li>
                <li>Si no está en uso, coloca el cursor sobre el número para ver a qué flujo está asignado</li>
                <li>Luego, vuelve al flujo en el que está enrutado ese número y elimínalo</li>
                <li>Regresa a Demo Atención General y asigna el número. Aparecerá un recuadro al lado, indicando que ha sido liberado correctamente</li>
              </ul>
            </div>

            {/* Step 3 */}
            <div className="border-l-4 border-blue-500 pl-4">
              <h3 className="font-semibold text-gray-900 mb-2">4. Guardar configuración</h3>
              <p className="text-gray-600">
                Para finalizar el enrutamiento del número, selecciona Guardar (Save).
              </p>
            </div>

            {/* Phone Numbers by Country */}
            <div className="bg-gray-50 rounded-lg p-6">
              <h4 className="font-semibold text-gray-900 mb-4">📞 Números por país</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                <div className="flex justify-between items-center py-2 px-3 bg-white rounded border">
                  <span className="font-medium text-gray-700">Chile:</span>
                  <span className="text-blue-600 font-mono">+56 2 2914 0364</span>
                </div>
                <div className="flex justify-between items-center py-2 px-3 bg-white rounded border">
                  <span className="font-medium text-gray-700">Perú:</span>
                  <span className="text-blue-600 font-mono">+51 1 510 2790</span>
                </div>
                <div className="flex justify-between items-center py-2 px-3 bg-white rounded border">
                  <span className="font-medium text-gray-700">Colombia:</span>
                  <span className="text-blue-600 font-mono">+57 601 589 8050</span>
                </div>
                <div className="flex justify-between items-center py-2 px-3 bg-white rounded border">
                  <span className="font-medium text-gray-700">Argentina:</span>
                  <span className="text-blue-600 font-mono">+54 11 3986 2190</span>
                </div>
                <div className="flex justify-between items-center py-2 px-3 bg-white rounded border">
                  <span className="font-medium text-gray-700">México:</span>
                  <span className="text-blue-600 font-mono">+52 55 9990 2872</span>
                </div>
              </div>
            </div>

            {/* Available Flows */}
            <div className="bg-gray-50 rounded-lg p-6">
              <h4 className="font-semibold text-gray-900 mb-4">🔄 Flujos Inbound disponibles</h4>
              <div className="space-y-2">
                <div className="flex items-center py-2 px-3 bg-white rounded border">
                  <div className="h-3 w-3 bg-green-500 rounded-full mr-3"></div>
                  <span className="text-gray-700">Demo Atención General</span>
                </div>
                <div className="flex items-center py-2 px-3 bg-white rounded border">
                  <div className="h-3 w-3 bg-blue-500 rounded-full mr-3"></div>
                  <span className="text-gray-700">Demo Agendamiento</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Section 2: Campaign Management */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200/50 p-6 mb-8">
          <div className="flex items-center mb-6">
            <div className="h-10 w-10 bg-purple-100 rounded-lg flex items-center justify-center mr-4">
              <Users className="h-5 w-5 text-purple-600" />
            </div>
            <div>
              <h2 className="text-xl font-bold text-gray-900">Configuración de una campaña de cobranza</h2>
              <p className="text-sm text-gray-500 mt-1">Ruta: Admin &gt; Campaign Management</p>
            </div>
          </div>

          <div className="space-y-6">
            {/* Step 1 */}
            <div className="border-l-4 border-purple-500 pl-4">
              <h3 className="font-semibold text-gray-900 mb-2">1. Buscar campaña existente</h3>
              <p className="text-gray-600">
                Busca la campaña "Demo Bot Cobranza Campaign..." y verifica qué Contact List está utilizando.
              </p>
            </div>

            {/* Step 2 */}
            <div className="border-l-4 border-purple-500 pl-4">
              <h3 className="font-semibold text-gray-900 mb-2">2. Editar lista de contactos</h3>
              <p className="text-gray-600">
                Selecciona la lista de contactos y haz clic en Edit Contact para agregar nuevos datos.
              </p>
            </div>

            {/* Step 3 */}
            <div className="border-l-4 border-purple-500 pl-4">
              <h3 className="font-semibold text-gray-900 mb-2">3. Agregar nuevo contacto</h3>
              <p className="text-gray-600 mb-4">
                Al ingresar, se mostrarán los contactos existentes. Para agregar un nuevo registro, selecciona Create New.
              </p>
              <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
                <p className="text-purple-800 font-medium">💡 Tip: Completa todos los campos requeridos y guarda los cambios.</p>
              </div>
            </div>

            {/* Step 4 */}
            <div className="border-l-4 border-purple-500 pl-4">
              <h3 className="font-semibold text-gray-900 mb-2">5. Activar campaña</h3>
              <p className="text-gray-600">
                Finalmente, vuelve a la sección de campañas y actívala.
              </p>
            </div>
          </div>
        </div>

        {/* Section 3: Digital Configuration */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200/50 p-6 mb-8">
          <div className="flex items-center mb-6">
            <div className="h-10 w-10 bg-emerald-100 rounded-lg flex items-center justify-center mr-4">
              <MessageSquare className="h-5 w-5 text-emerald-600" />
            </div>
            <div>
              <h2 className="text-xl font-bold text-gray-900">Configura número de teléfono para sección digital</h2>
              <p className="text-sm text-gray-500 mt-1">Ruta: Admin &gt; Architect &gt; Inbound Message &gt; Whatsapp CrossnetSA</p>
            </div>
          </div>

          <div className="space-y-6">
            {/* Step 1 */}
            <div className="border-l-4 border-emerald-500 pl-4">
              <h3 className="font-semibold text-gray-900 mb-2">1. Acceder al flujo de WhatsApp</h3>
              <p className="text-gray-600 mb-4">
                Para configurar tu número de teléfono y enrutarlo al bot correspondiente, debes seleccionar el botón "editar"
                y luego buscar el siguiente bloque que se llama "cambiar" y presenta un switch.
              </p>
              <div className="bg-gray-50 rounded-lg p-4">
                <img
                  src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAwIiBoZWlnaHQ9IjQwMCIgdmlld0JveD0iMCAwIDYwMCA0MDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSI2MDAiIGhlaWdodD0iNDAwIiBmaWxsPSIjRjlGQUZCIi8+CjxyZWN0IHg9IjIwIiB5PSIyMCIgd2lkdGg9IjU2MCIgaGVpZ2h0PSI2MCIgZmlsbD0iIzM3NDE1MSIvPgo8dGV4dCB4PSI0MCIgeT0iNTUiIGZpbGw9IndoaXRlIiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMTgiIGZvbnQtd2VpZ2h0PSJib2xkIj5BcmNoaXRlY3QgLyBJbmJvdW5kIE1lc3NhZ2UgRmxvdzwvdGV4dD4KPHJlY3QgeD0iNDAiIHk9IjEwMCIgd2lkdGg9IjEyMCIgaGVpZ2h0PSI4MCIgZmlsbD0iIzMzMzhGRiIgcng9IjgiLz4KPHRleHQgeD0iNjAiIHk9IjEzMCIgZmlsbD0id2hpdGUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxMiI+SW5pdGlhbCBTdGF0ZTwvdGV4dD4KPHRleHQgeD0iNzAiIHk9IjE1MCIgZmlsbD0id2hpdGUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZm9udC13ZWlnaHQ9ImJvbGQiPjEwPC90ZXh0Pgo8cmVjdCB4PSIyNDAiIHk9IjEwMCIgd2lkdGg9IjE0MCIgaGVpZ2h0PSI4MCIgZmlsbD0iIzEwQjk4MSIgcng9IjgiLz4KPHRleHQgeD0iMjYwIiB5PSIxMzAiIGZpbGw9IndoaXRlIiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMTIiPkNhbWJpYXI8L3RleHQ+CjxyZWN0IHg9IjQ0MCIgeT0iMTAwIiB3aWR0aD0iMTIwIiBoZWlnaHQ9IjgwIiBmaWxsPSIjRjU5RTBCIiByeD0iOCIvPgo8dGV4dCB4PSI0NjAiIHk9IjEzMCIgZmlsbD0id2hpdGUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxMiI+U3dpdGNoPC90ZXh0Pgo8dGV4dCB4PSI0NzAiIHk9IjE1MCIgZmlsbD0id2hpdGUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZm9udC13ZWlnaHQ9ImJvbGQiPjI1PC90ZXh0Pgo8L3N2Zz4="
                  alt="Architect Flow Interface"
                  className="w-full rounded border"
                />
              </div>
            </div>

            {/* Step 2 */}
            <div className="border-l-4 border-emerald-500 pl-4">
              <h3 className="font-semibold text-gray-900 mb-2">2. Configurar casos 29 y 30</h3>
              <p className="text-gray-600 mb-4">
                En este bloque debes buscar el caso 29 y 30, donde estarán enrutados los flujos agendamiento y atención general,
                luego debes seleccionar la flechita para poder agregar un número nuevo y luego agregar:
              </p>
              <div className="bg-emerald-50 border border-emerald-200 rounded-lg p-4">
                <code className="text-emerald-800 font-mono text-sm">
                  or Message.Message.senderAddress=="NUMERO_NUEVO"
                </code>
              </div>
              <p className="text-gray-600 mt-2">y cerrar.</p>
            </div>

            {/* Step 3 */}
            <div className="border-l-4 border-emerald-500 pl-4">
              <h3 className="font-semibold text-gray-900 mb-2">3. Publicar cambios</h3>
              <p className="text-gray-600">
                Finalmente publica los cambios.
              </p>
            </div>

            {/* Code Example */}
            <div className="bg-gray-50 rounded-lg p-6">
              <h4 className="font-semibold text-gray-900 mb-4">💻 Ejemplo de configuración</h4>
              <div className="bg-gray-800 rounded-lg p-4">
                <code className="text-green-400 font-mono text-sm block">
                  Message.Message.senderAddress=="56998641945" or Message.Message.senderAddress=="NUMERO_NUEVO"
                </code>
              </div>
              <p className="text-gray-600 text-sm mt-2">
                Reemplaza "NUMERO_NUEVO" con el número de WhatsApp que deseas configurar.
              </p>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl border border-blue-200/50 p-6">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="font-semibold text-blue-900 mb-1">¿Necesitas ayuda adicional?</h3>
              <p className="text-blue-700 text-sm">
                Contacta al equipo de soporte técnico para asistencia personalizada.
              </p>
            </div>
            <button className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
              <ExternalLink className="h-4 w-4 mr-2" />
              Soporte
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default GenesysCloudDocs;
