import { Bot } from '../types/bot';

export const bots: Bot[] = [
  {
    id: 'demos-bot-cobranza',
    name: '<PERSON><PERSON> <PERSON>',
    description: 'Automatiza el proceso de cobranza con recordatorios personalizados y gestión eficiente de pagos pendientes.',
    fullDescription: 'Nuestro bot de cobranza está diseñado para optimizar el proceso de recuperación de cartera de manera respetuosa y efectiva. Utiliza técnicas de comunicación personalizadas para contactar a clientes con pagos pendientes, ofrece opciones de pago flexibles y mantiene un registro detallado de todas las interacciones. Puede programar recordatorios automáticos, negociar planes de pago y escalar casos complejos al equipo humano cuando sea necesario.',
    category: 'Cobranza',
    endpoint: 'https://api.cobranza-bot.com/v1',
    contactNumber: '******-COBRANZA',
    conversationFlow: [
      {
        userInput: 'Hola. Te llamamos de Financiera X ¿Me comunico con $nombre $apellido?',
        botResponse: 'Si, con ella / el',
        expectedResult: 'Debe presentarse y preguntar si se comunica con el usuario.'
      },
      {
        userInput: 'Tiene un pago vencido desde el $vencimiento por un total de $deuda. Por ser cliente con buen historial, hoy tiene acceso a un descuento especial por pronto pago, válido solo hasta el $plazo. El nuevo monto con descuento es de $propuesta. ¿Le gustaría aprovechar este beneficio exclusivo y regularizar hoy su situación con el monto reducido?',
        botResponse: 'Si, me interesa',
        expectedResult: 'Debe identificar respuesta seleccionada'
      },
      {
        userInput: '¡Excelente! Este beneficio está disponible hasta $plazo y puede realizar el pago desde la app de "Financiera X". Este descuento es por única vez, y le permite evitar recargos futuros y mantener su historial limpio. ¿Le gustaría que le enviemos ahora mismo las instrucciones para pagar a través de correo electrónico?',
        botResponse: 'Está bien.',
        expectedResult: 'Debe identificar respuesta seleccionada'
      },
      {
        userInput: 'Antes de enviarle la información, ¿podría confirmarme si su dirección de correo actual es $correo?',
        botResponse: 'Si, ese es / no, ese no es',
        expectedResult: 'Debe identificar respuesta seleccionada'
      },
      {
        userInput: 'La información fue enviada con éxito. Muchas gracias por su atención, que tenga un buen día.',
        botResponse: '',
        expectedResult: 'Despedida'
      }
    ],
    icon: 'CreditCard',
    color: 'bg-red-500',
    imageUrl: 'https://images.pexels.com/photos/1560932/pexels-photo-1560932.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2'
  },
  {
    id: 'demos-bot-informacion-general',
    name: 'Bot de Información General',
    description: 'Proporciona información completa sobre productos, servicios, políticas y procedimientos de la empresa.',
    fullDescription: 'Este bot centraliza toda la información corporativa y está preparado para responder consultas generales sobre la empresa, sus productos, servicios, políticas, horarios de atención, ubicaciones y procedimientos. Cuenta con una base de conocimientos actualizada constantemente y puede proporcionar información detallada sobre especificaciones técnicas, precios, disponibilidad y procesos internos. Es ideal para reducir la carga de consultas repetitivas en el equipo de atención al cliente.',
    category: 'Información General',
    endpoint: 'https://api.info-bot.com/v1',
    contactNumber: '******-INFO',
    conversationFlow: [
      {
        userInput: 'Hola (default)',
        botResponse: 'Hola, ¿cómo puedo asistirte hoy?',
        expectedResult: 'Debe saludar y mostrar interés en que puede ayudar'
      },
      {
        userInput: 'cuantas sucursales ustedes tienen?',
        botResponse: 'Tenemos sucursales en Chile, Perú, Argentina, Colombia y México.',
        expectedResult: 'Debe responder en base a la intención identificada'
      },
      {
        userInput: 'me gustaria saber la direccion de chile',
        botResponse: 'En Chile, nos encontramos en Apoquindo 3039, piso 9, Las Condes, Santiago.',
        expectedResult: 'Debe responder en base a la intención identificada'
      },
      {
        userInput: 'y el horario de atencion?',
        botResponse: 'Nuestro horario de atención es de 9 de la mañana a 6 de la tarde, hora local.',
        expectedResult: 'Debe responder en base a la intención identificada'
      },
      {
        userInput: 'me puedes pasar con un agente de venta?',
        botResponse: 'Con gusto puedo derivarte con un agente humano. Por favor espera un momento mientras te conectamos.',
        expectedResult: 'Debe responder en base a la intención identificada'
      }
    ],
    icon: 'Info',
    color: 'bg-blue-500',
    imageUrl: 'https://images.pexels.com/photos/1007410/pexels-photo-1007410.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2'
  },
  {
    id: 'demos-bot-servicios',
    name: 'Bot de Servicios',
    description: 'Especializado en proporcionar información sobre productos y servicios, incluyendo el Orquestador de Campañas de Crossnet.',
    fullDescription: 'Este bot está especializado en brindar información detallada sobre los productos y servicios que ofrecemos, incluyendo soluciones de Omnicanalidad, Inteligencia Artificial y Analytics. Puede explicar las características del Orquestador de Campañas de Crossnet y guiar a los clientes en el proceso de contratación.',
    category: 'Servicios',
    endpoint: 'https://api.services-bot.com/v1',
    contactNumber: '******-SERVICIOS',
    conversationFlow: [
      {
        userInput: 'Hola (default)',
        botResponse: 'Hola, ¿cómo puedo asistirte hoy?',
        expectedResult: 'Debe saludar y mostrar interés en que puede ayudar'
      },
      {
        userInput: '¿Quiero saber cuales productos vendes?',
        botResponse: 'Ofrecemos soluciones de Omnicanalidad, Inteligencia Artificial y Analytics. Esto incluye una experiencia de atención al cliente unificada desde cualquier canal, automatización de la atención en tiempo real y optimización de recursos, y análisis para conocer mejor a tus clientes y mejorar la toma de decisiones.',
        expectedResult: 'Debe responder en base a la intención identificada'
      },
      {
        userInput: 'y me puedes hablar del orquestador?',
        botResponse: 'El Orquestador de Campañas de Crossnet te permite gestionar grandes bases de datos, modelar procesos y definir reglas de contacto, optimizando el uso de recursos y minimizando costos. Permite cargar bases, modelar procesos, definir reglas de discado, alarmas y generar reportes centralizados para todas tus campañas, sin importar el canal o plataforma.',
        expectedResult: 'Debe responder en base a la intención identificada'
      },
      {
        userInput: 'quiero contratarlo',
        botResponse: 'Para contratarlo, te sugiero visitar nuestra web oficial crossnet.la o escribir al formulario de contacto.',
        expectedResult: 'Debe identificar opción seleccionada'
      }
    ],
    icon: 'Settings',
    color: 'bg-purple-500',
    imageUrl: 'https://images.pexels.com/photos/3861969/pexels-photo-3861969.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2'
  },
  {
    id: 'demos-bot-agendamiento',
    name: 'Bot de Agendamiento',
    description: 'Asistente de Agendamiento ayuda a los clientes a agendar una cita médica en la clínica Crossnet',
    fullDescription: 'Asistente de Agendamiento ayuda a los clientes a agendar una cita médica en la clínica Crossnet.',
    category: 'Agendamiento',
    endpoint: 'https://api.scheduling-bot.com/v1',
    contactNumber: '+57-1-800-CROSSNET',
    conversationFlow: [
      {
        userInput: 'Hola (default)',
        botResponse: 'Hola, bienvenido a la clínica Crossnet. ¿En qué puedo ayudarle hoy?',
        expectedResult: 'Debe presentarse y decir que es de la clínica Crossnet.'
      },
      {
        userInput: 'Quiero agendar una cita médica',
        botResponse: '¡Perfecto! Para agendar una cita, necesito algunos datos. Por favor, ¿me podría proporcionar su número de DNI?',
        expectedResult: 'Debe identificar intención y solicitar DNI'
      },
      {
        userInput: 'Mi dni es 12345',
        botResponse: 'Gracias. ¿La cita es para usted o para un dependiente?',
        expectedResult: 'Debe identificar opción seleccionada'
      },
      {
        userInput: 'Para mí / Para dependiente',
        botResponse: '¿Desea agendar la cita con un médico en particular o con alguna especialidad?',
        expectedResult: 'Debe identificar opción seleccionada'
      },
      {
        userInput: 'Con la dra Loyola, por favor.',
        botResponse: 'Ok, seria con la Doctora Loyola. ¿Para qué hora le gustaría agendar la cita? Tenemos estos horarios disponibles: 9:00 a. m., 10:30 a. m. y 3:15 p. m.',
        expectedResult: 'Debe identificar opción seleccionada'
      },
      {
        userInput: 'A las 10:30 am por favor',
        botResponse: 'Perfecto. Y, ¿con qué aseguradora tiene cobertura? Las opciones son: Aseguradora 1 y Aseguradora 2.',
        expectedResult: 'Debe identificar opción seleccionada'
      },
      {
        userInput: 'Con la aseguradora 1 / aseguradora 2',
        botResponse: 'Muchas gracias...',
        expectedResult: 'Despedida'
      }
    ],
    icon: 'Calendar',
    color: 'bg-green-500',
    imageUrl: 'https://images.pexels.com/photos/3985163/pexels-photo-3985163.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2'
  }
];