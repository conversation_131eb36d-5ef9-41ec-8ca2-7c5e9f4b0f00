// src/App.tsx
import React, { useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { bots } from './data/bots';
import Header from './components/Header';
import Sidebar from './components/Sidebar';
import AppRouter from './components/AppRouter';
import { hasDialogflowConfig } from './config/dialogflow';

function App() {
  const navigate = useNavigate();
  const location = useLocation();
  const [searchTerm, setSearchTerm] = useState('');
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const [isDialogflowWidgetOpen, setIsDialogflowWidgetOpen] = useState(false);
  const [dialogflowKey, setDialogflowKey] = useState(0); // Key para forzar re-render del DialogflowChat



  // Obtener el botId actual de la URL
  const getCurrentBotId = (): string | null => {
    const pathParts = location.pathname.split('/');
    if (pathParts[1] === 'bot' && pathParts[2]) {
      return pathParts[2];
    }
    return null;
  };

  const handleBotSelect = (botId: string) => {
    setIsDialogflowWidgetOpen(false);
    setSidebarOpen(false);
    setSearchTerm('');
    navigate(`/bot/${botId}`);
  };

  const handleBotDetail = (botId: string) => {
    setIsDialogflowWidgetOpen(false);
    setSearchTerm('');
    navigate(`/bot/${botId}`);
  };

  const handleStartChat = (botId: string) => {
    console.log(`[App] handleStartChat called for botId: ${botId}`);
    setSearchTerm('');

    // Verificar si el bot tiene configuración de Dialogflow
    if (hasDialogflowConfig(botId)) {
      console.log(`[App] Bot ${botId} has Dialogflow config, navigating to detail page with chat=true`);
      // Para bots con Dialogflow, ir a la página de detalle con parámetro para abrir widget
      navigate(`/bot/${botId}?chat=true`);
    } else {
      console.log(`[App] Bot ${botId} does not have Dialogflow config, navigating to chat`);
      // Bot sin Dialogflow va directo al chat
      navigate(`/bot/${botId}/chat`);
    }
  };

  const handleUseFallback = () => {
    const currentBotId = getCurrentBotId();
    if (currentBotId) {
      navigate(`/bot/${currentBotId}/chat`);
      setIsDialogflowWidgetOpen(false);
    }
  };

  // Determinar si estamos en una página de chat (para ocultar header y sidebar)
  const isInChatPage = location.pathname.includes('/chat');

  return (
    <div className="min-h-screen bg-gray-50">
      {!isInChatPage && (
        <>
          <Header
            searchTerm={searchTerm}
            onSearchChange={setSearchTerm}
            onMenuToggle={() => setSidebarOpen(!sidebarOpen)}
            showSearch={location.pathname === '/'}
          />

          <div className="flex pt-16">
            <Sidebar
              bots={bots}
              selectedBotId={getCurrentBotId()}
              onBotSelect={handleBotSelect}
              isOpen={sidebarOpen}
              onClose={() => setSidebarOpen(false)}
            />

            <main className="flex-1 ml-80 relative overflow-hidden">
              <AppRouter
                searchTerm={searchTerm}
                onBotDetail={handleBotDetail}
                onBotDemo={handleStartChat}
                onStartChat={handleStartChat}
                onUseFallback={handleUseFallback}
                isDialogflowWidgetOpen={isDialogflowWidgetOpen}
                setIsDialogflowWidgetOpen={setIsDialogflowWidgetOpen}
                dialogflowKey={dialogflowKey}
              />
            </main>
          </div>
        </>
      )}

      {isInChatPage && (
        <AppRouter
          searchTerm={searchTerm}
          onBotDetail={handleBotDetail}
          onBotDemo={handleStartChat}
          onStartChat={handleStartChat}
          onUseFallback={handleUseFallback}
          isDialogflowWidgetOpen={isDialogflowWidgetOpen}
          setIsDialogflowWidgetOpen={setIsDialogflowWidgetOpen}
          dialogflowKey={dialogflowKey}
        />
      )}
    </div>
  );
}

export default App;
