import { useState, useEffect, useCallback } from 'react';
import { Message } from '../types/bot';

export interface ConversationData {
  botId: string;
  messages: Message[];
  lastUpdated: Date;
  sessionId: string;
}

export interface ConversationStorage {
  conversations: Record<string, ConversationData>;
  settings: {
    maxConversations: number;
    maxMessagesPerConversation: number;
    autoSave: boolean;
    retentionDays: number;
  };
}

const STORAGE_KEY = 'crossnet_bot_conversations';
const DEFAULT_SETTINGS = {
  maxConversations: 10,
  maxMessagesPerConversation: 100,
  autoSave: true,
  retentionDays: 30
};

/**
 * Hook para manejar la persistencia de conversaciones en localStorage
 * Permite guardar, cargar y gestionar conversaciones de chat
 */
export const useConversationPersistence = (botId: string) => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [sessionId] = useState(() => `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`);
  const [isLoading, setIsLoading] = useState(true);

  // Generar clave única para la conversación
  const conversationKey = `${botId}_${sessionId}`;

  // Cargar datos del localStorage
  const loadFromStorage = useCallback((): ConversationStorage => {
    try {
      const stored = localStorage.getItem(STORAGE_KEY);
      if (stored) {
        const parsed = JSON.parse(stored);
        // Convertir fechas de string a Date
        Object.values(parsed.conversations || {}).forEach((conv: any) => {
          conv.lastUpdated = new Date(conv.lastUpdated);
          conv.messages.forEach((msg: any) => {
            msg.timestamp = new Date(msg.timestamp);
          });
        });
        return {
          conversations: parsed.conversations || {},
          settings: { ...DEFAULT_SETTINGS, ...parsed.settings }
        };
      }
    } catch (error) {
      console.error('Error loading conversations from localStorage:', error);
    }
    
    return {
      conversations: {},
      settings: DEFAULT_SETTINGS
    };
  }, []);

  // Guardar datos en localStorage
  const saveToStorage = useCallback((data: ConversationStorage) => {
    try {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(data));
    } catch (error) {
      console.error('Error saving conversations to localStorage:', error);
    }
  }, []);

  // Limpiar conversaciones antiguas
  const cleanOldConversations = useCallback((storage: ConversationStorage) => {
    const now = new Date();
    const retentionMs = storage.settings.retentionDays * 24 * 60 * 60 * 1000;
    
    const cleanedConversations: Record<string, ConversationData> = {};
    
    Object.entries(storage.conversations).forEach(([key, conversation]) => {
      const age = now.getTime() - conversation.lastUpdated.getTime();
      if (age < retentionMs) {
        cleanedConversations[key] = conversation;
      }
    });

    return {
      ...storage,
      conversations: cleanedConversations
    };
  }, []);

  // Cargar conversación existente para el bot
  const loadConversation = useCallback(() => {
    setIsLoading(true);
    const storage = loadFromStorage();
    
    // Buscar conversación más reciente para este bot
    const botConversations = Object.values(storage.conversations)
      .filter(conv => conv.botId === botId)
      .sort((a, b) => b.lastUpdated.getTime() - a.lastUpdated.getTime());
    
    if (botConversations.length > 0) {
      setMessages(botConversations[0].messages);
    } else {
      setMessages([]);
    }
    
    setIsLoading(false);
  }, [botId, loadFromStorage]);

  // Guardar conversación actual
  const saveConversation = useCallback((messagesToSave: Message[]) => {
    if (messagesToSave.length === 0) return;

    const storage = loadFromStorage();
    const cleanedStorage = cleanOldConversations(storage);
    
    // Limitar número de mensajes por conversación
    const limitedMessages = messagesToSave.slice(-cleanedStorage.settings.maxMessagesPerConversation);
    
    // Actualizar o crear conversación
    cleanedStorage.conversations[conversationKey] = {
      botId,
      messages: limitedMessages,
      lastUpdated: new Date(),
      sessionId
    };

    // Limitar número total de conversaciones
    const conversationEntries = Object.entries(cleanedStorage.conversations);
    if (conversationEntries.length > cleanedStorage.settings.maxConversations) {
      // Mantener solo las conversaciones más recientes
      const sortedConversations = conversationEntries
        .sort(([, a], [, b]) => b.lastUpdated.getTime() - a.lastUpdated.getTime())
        .slice(0, cleanedStorage.settings.maxConversations);
      
      cleanedStorage.conversations = Object.fromEntries(sortedConversations);
    }

    saveToStorage(cleanedStorage);
  }, [botId, sessionId, conversationKey, loadFromStorage, cleanOldConversations, saveToStorage]);

  // Eliminar conversación específica
  const deleteConversation = useCallback((conversationKeyToDelete?: string) => {
    const storage = loadFromStorage();
    const keyToDelete = conversationKeyToDelete || conversationKey;
    
    delete storage.conversations[keyToDelete];
    saveToStorage(storage);
    
    if (keyToDelete === conversationKey) {
      setMessages([]);
    }
  }, [conversationKey, loadFromStorage, saveToStorage]);

  // Obtener todas las conversaciones para un bot
  const getBotConversations = useCallback(() => {
    const storage = loadFromStorage();
    return Object.values(storage.conversations)
      .filter(conv => conv.botId === botId)
      .sort((a, b) => b.lastUpdated.getTime() - a.lastUpdated.getTime());
  }, [botId, loadFromStorage]);

  // Limpiar todas las conversaciones
  const clearAllConversations = useCallback(() => {
    const storage = loadFromStorage();
    storage.conversations = {};
    saveToStorage(storage);
    setMessages([]);
  }, [loadFromStorage, saveToStorage]);

  // Actualizar configuración
  const updateSettings = useCallback((newSettings: Partial<typeof DEFAULT_SETTINGS>) => {
    const storage = loadFromStorage();
    storage.settings = { ...storage.settings, ...newSettings };
    saveToStorage(storage);
  }, [loadFromStorage, saveToStorage]);

  // Cargar conversación al montar el componente
  useEffect(() => {
    loadConversation();
  }, [loadConversation]);

  // Auto-guardar cuando cambian los mensajes
  useEffect(() => {
    if (!isLoading && messages.length > 0) {
      const storage = loadFromStorage();
      if (storage.settings.autoSave) {
        const timeoutId = setTimeout(() => {
          saveConversation(messages);
        }, 1000); // Debounce de 1 segundo

        return () => clearTimeout(timeoutId);
      }
    }
  }, [messages, isLoading, saveConversation, loadFromStorage]);

  return {
    messages,
    setMessages,
    isLoading,
    sessionId,
    saveConversation,
    deleteConversation,
    getBotConversations,
    clearAllConversations,
    updateSettings,
    loadConversation
  };
};
