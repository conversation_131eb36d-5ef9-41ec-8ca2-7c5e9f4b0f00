import React, { useEffect, useState } from 'react';

interface PageTransitionProps {
  children: React.ReactNode;
  isVisible: boolean;
  type?: 'fade' | 'slide-right' | 'slide-left' | 'slide-up' | 'slide-down' | 'scale';
  duration?: number;
  delay?: number;
  className?: string;
}

const PageTransition: React.FC<PageTransitionProps> = ({
  children,
  isVisible,
  type = 'fade',
  duration = 300,
  delay = 0,
  className = ''
}) => {
  const [shouldRender, setShouldRender] = useState(isVisible);
  const [animationClass, setAnimationClass] = useState('');

  useEffect(() => {
    if (isVisible) {
      setShouldRender(true);
      // Pequeño delay para asegurar que el elemento esté en el DOM antes de animar
      setTimeout(() => {
        setAnimationClass(getEnterClass(type));
      }, 10);
    } else {
      setAnimationClass(getExitClass(type));
      // Esperar a que termine la animación antes de desmontar
      setTimeout(() => {
        setShouldRender(false);
      }, duration);
    }
  }, [isVisible, type, duration]);

  const getEnterClass = (transitionType: string): string => {
    switch (transitionType) {
      case 'fade':
        return 'opacity-100 translate-y-0';
      case 'slide-right':
        return 'opacity-100 translate-x-0';
      case 'slide-left':
        return 'opacity-100 -translate-x-0';
      case 'slide-up':
        return 'opacity-100 translate-y-0';
      case 'slide-down':
        return 'opacity-100 -translate-y-0';
      case 'scale':
        return 'opacity-100 scale-100';
      default:
        return 'opacity-100 translate-y-0';
    }
  };

  const getExitClass = (transitionType: string): string => {
    switch (transitionType) {
      case 'fade':
        return 'opacity-0 translate-y-2';
      case 'slide-right':
        return 'opacity-0 translate-x-full';
      case 'slide-left':
        return 'opacity-0 -translate-x-full';
      case 'slide-up':
        return 'opacity-0 -translate-y-full';
      case 'slide-down':
        return 'opacity-0 translate-y-full';
      case 'scale':
        return 'opacity-0 scale-95';
      default:
        return 'opacity-0 translate-y-2';
    }
  };

  const getInitialClass = (transitionType: string): string => {
    switch (transitionType) {
      case 'fade':
        return 'opacity-0 translate-y-2';
      case 'slide-right':
        return 'opacity-0 -translate-x-full';
      case 'slide-left':
        return 'opacity-0 translate-x-full';
      case 'slide-up':
        return 'opacity-0 translate-y-full';
      case 'slide-down':
        return 'opacity-0 -translate-y-full';
      case 'scale':
        return 'opacity-0 scale-95';
      default:
        return 'opacity-0 translate-y-2';
    }
  };

  if (!shouldRender) {
    return null;
  }

  const transitionStyle = {
    transitionDuration: `${duration}ms`,
    transitionDelay: `${delay}ms`,
    transitionTimingFunction: 'cubic-bezier(0.4, 0, 0.2, 1)'
  };

  return (
    <div
      className={`
        transition-all transform
        ${animationClass || getInitialClass(type)}
        ${className}
      `}
      style={transitionStyle}
    >
      {children}
    </div>
  );
};

export default PageTransition;
