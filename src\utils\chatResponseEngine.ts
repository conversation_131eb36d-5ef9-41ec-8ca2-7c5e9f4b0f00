import { Bot, Message } from '../types/bot';

export interface ResponseContext {
  previousMessages: Message[];
  currentInput: string;
  bot: Bot;
  conversationState: ConversationState;
}

export interface ConversationState {
  stage: 'greeting' | 'information_gathering' | 'problem_solving' | 'closing';
  userIntent: string | null;
  collectedInfo: Record<string, any>;
  lastBotAction: string | null;
}

export interface EnhancedResponse {
  content: string;
  suggestions?: string[];
  actions?: ResponseAction[];
  metadata?: {
    confidence: number;
    intent: string;
    nextStage?: string;
  };
}

export interface ResponseAction {
  type: 'link' | 'phone' | 'email' | 'schedule';
  label: string;
  value: string;
}

/**
 * Motor de respuestas inteligente para el chat interno
 * Proporciona respuestas contextuales y personalizadas basadas en el bot y la conversación
 */
export class ChatResponseEngine {
  private intentPatterns: Record<string, RegExp[]> = {
    greeting: [
      /\b(hola|hello|hi|buenos días|buenas tardes|buenas noches)\b/i,
      /\b(saludos|qué tal|cómo estás)\b/i
    ],
    pricing: [
      /\b(precio|costo|cuánto cuesta|tarifa|cotización)\b/i,
      /\b(cuánto vale|valor|presupuesto)\b/i
    ],
    schedule: [
      /\b(horario|hora|cuándo|disponibilidad|cita|agenda)\b/i,
      /\b(programar|agendar|reservar)\b/i
    ],
    problem: [
      /\b(problema|error|falla|no funciona|ayuda)\b/i,
      /\b(soporte|asistencia|dificultad)\b/i
    ],
    information: [
      /\b(información|detalles|características|funciones)\b/i,
      /\b(qué es|cómo funciona|para qué sirve)\b/i
    ],
    contact: [
      /\b(contacto|teléfono|email|dirección|ubicación)\b/i,
      /\b(hablar con|comunicar|llamar)\b/i
    ],
    thanks: [
      /\b(gracias|thank you|muchas gracias|te agradezco)\b/i
    ],
    goodbye: [
      /\b(adiós|hasta luego|nos vemos|chao|bye)\b/i
    ]
  };

  private botSpecificResponses: Record<string, Record<string, string[]>> = {
    'demos-bot-cobranza': {
      greeting: [
        'Hola, soy tu asistente de cobranza. Estoy aquí para ayudarte con consultas sobre pagos y gestión de cartera.',
        '¡Bienvenido! Puedo ayudarte con información sobre estados de cuenta, opciones de pago y planes de financiamiento.'
      ],
      pricing: [
        'Nuestros servicios de cobranza se adaptan a las necesidades de cada empresa. ¿Te gustaría conocer nuestros planes?',
        'Ofrecemos diferentes modalidades de cobranza. ¿Podrías contarme más sobre el volumen de tu cartera?'
      ],
      information: [
        'Nuestro sistema de cobranza automatiza recordatorios, gestiona planes de pago y mantiene comunicación respetuosa con los deudores.',
        'Utilizamos IA para personalizar estrategias de cobranza y maximizar la recuperación de cartera de manera ética.'
      ]
    },
    'demos-bot-informacion-general': {
      greeting: [
        'Hola, soy tu asistente de información general. Puedo ayudarte con consultas sobre nuestros servicios y productos.',
        '¡Bienvenido! Estoy aquí para brindarte información sobre Crossnet y nuestras soluciones tecnológicas.'
      ],
      information: [
        'Crossnet ofrece soluciones de Omnicanalidad, Inteligencia Artificial y Analytics para empresas.',
        'Nuestro Orquestador de Campañas permite gestionar comunicaciones multicanal de manera eficiente.'
      ]
    },
    'demos-bot-servicios': {
      greeting: [
        'Hola, soy tu especialista en servicios. Puedo ayudarte con información detallada sobre nuestros productos.',
        '¡Bienvenido! Estoy aquí para explicarte nuestras soluciones y cómo pueden beneficiar a tu empresa.'
      ],
      information: [
        'Nuestro Orquestador de Campañas integra múltiples canales de comunicación en una sola plataforma.',
        'Ofrecemos soluciones de IA conversacional, analytics avanzados y automatización de procesos.'
      ]
    },
    'demos-bot-agendamiento': {
      greeting: [
        'Hola, soy tu asistente de agendamiento. Puedo ayudarte a programar citas y gestionar tu calendario.',
        '¡Bienvenido! Estoy aquí para facilitar la programación de reuniones y citas.'
      ],
      schedule: [
        'Puedo ayudarte a agendar una cita. ¿Qué tipo de reunión necesitas programar?',
        'Tengo disponibilidad para diferentes tipos de citas. ¿Cuándo te gustaría reunirte?'
      ]
    }
  };

  detectIntent(input: string): string {
    const normalizedInput = input.toLowerCase();
    
    for (const [intent, patterns] of Object.entries(this.intentPatterns)) {
      for (const pattern of patterns) {
        if (pattern.test(normalizedInput)) {
          return intent;
        }
      }
    }
    
    return 'general';
  }

  generateResponse(context: ResponseContext): EnhancedResponse {
    const intent = this.detectIntent(context.currentInput);
    const botResponses = this.botSpecificResponses[context.bot.id] || {};
    
    let content = '';
    let suggestions: string[] = [];
    let actions: ResponseAction[] = [];
    
    // Generar respuesta basada en el intent y el bot
    if (botResponses[intent]) {
      const responses = botResponses[intent];
      content = responses[Math.floor(Math.random() * responses.length)];
    } else {
      content = this.generateGenericResponse(intent, context);
    }
    
    // Agregar sugerencias contextuales
    suggestions = this.generateSuggestions(intent, context.bot);
    
    // Agregar acciones si es apropiado
    actions = this.generateActions(intent, context.bot);
    
    return {
      content,
      suggestions,
      actions,
      metadata: {
        confidence: 0.8,
        intent,
        nextStage: this.getNextStage(intent, context.conversationState.stage)
      }
    };
  }

  private generateGenericResponse(intent: string, context: ResponseContext): string {
    const { bot } = context;
    
    switch (intent) {
      case 'greeting':
        return `¡Hola! Soy ${bot.name}. ${bot.description} ¿En qué puedo ayudarte hoy?`;
      
      case 'pricing':
        return 'Los precios varían según tus necesidades específicas. ¿Podrías contarme más sobre lo que estás buscando para darte una cotización personalizada?';
      
      case 'schedule':
        return 'Puedo ayudarte con información sobre horarios y disponibilidad. ¿Qué tipo de cita o consulta necesitas programar?';
      
      case 'problem':
        return 'Lamento escuchar que tienes un problema. Describe con detalle la situación para poder ayudarte de la mejor manera.';
      
      case 'information':
        return `Como ${bot.name}, puedo proporcionarte información detallada sobre ${bot.category.toLowerCase()}. ¿Qué aspecto específico te interesa conocer?`;
      
      case 'contact':
        return bot.contactNumber 
          ? `Puedes contactarnos al ${bot.contactNumber} o continuar aquí conmigo. ¿Prefieres que te ayude directamente?`
          : 'Puedo ayudarte con tu consulta directamente aquí. ¿Qué información necesitas?';
      
      case 'thanks':
        return '¡De nada! Me alegra poder ayudarte. ¿Hay algo más en lo que pueda asistirte?';
      
      case 'goodbye':
        return '¡Hasta luego! Ha sido un placer ayudarte. No dudes en volver si necesitas más información.';
      
      default:
        return `Entiendo tu consulta sobre ${bot.category.toLowerCase()}. ¿Podrías proporcionar más detalles para ayudarte mejor?`;
    }
  }

  private generateSuggestions(intent: string, bot: Bot): string[] {
    const baseSuggestions = {
      greeting: ['Ver servicios', 'Solicitar información', 'Hablar con un agente'],
      pricing: ['Ver planes', 'Solicitar cotización', 'Comparar opciones'],
      schedule: ['Agendar cita', 'Ver disponibilidad', 'Cambiar cita'],
      problem: ['Describir problema', 'Hablar con soporte', 'Ver soluciones'],
      information: ['Ver características', 'Solicitar demo', 'Descargar brochure'],
      contact: ['Llamar ahora', 'Enviar email', 'Agendar llamada']
    };

    return baseSuggestions[intent as keyof typeof baseSuggestions] || ['¿Puedes ser más específico?', 'Cuéntame más', 'Ver opciones'];
  }

  private generateActions(intent: string, bot: Bot): ResponseAction[] {
    const actions: ResponseAction[] = [];
    
    if (intent === 'contact' && bot.contactNumber) {
      actions.push({
        type: 'phone',
        label: 'Llamar ahora',
        value: bot.contactNumber
      });
    }
    
    if (intent === 'schedule') {
      actions.push({
        type: 'schedule',
        label: 'Agendar cita',
        value: 'schedule_appointment'
      });
    }
    
    if (intent === 'information' && bot.endpoint) {
      actions.push({
        type: 'link',
        label: 'Más información',
        value: bot.endpoint
      });
    }
    
    return actions;
  }

  private getNextStage(intent: string, currentStage: string): string {
    const stageTransitions: Record<string, string> = {
      greeting: 'information_gathering',
      information: 'problem_solving',
      problem: 'problem_solving',
      pricing: 'problem_solving',
      schedule: 'closing',
      thanks: 'closing',
      goodbye: 'closing'
    };
    
    return stageTransitions[intent] || currentStage;
  }
}
