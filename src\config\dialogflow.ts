/**
 * Configuración de Dialogflow para cada bot
 *
 * Para agregar un nuevo bot con Dialogflow:
 * 1. <PERSON><PERSON><PERSON> el agente en Google Cloud Dialogflow
 * 2. Obtener el project-id y agent-id del agente
 * 3. Agregar la configuración al botAgentMap
 * 4. El bot aparecerá automáticamente en la lista de bots habilitados para Dialogflow
 */
export interface DialogflowConfig {
  projectId: string;  // ID del proyecto en Google Cloud
  agentId: string;    // ID único del agente de Dialogflow
  chatTitle: string;  // Título que aparece en el widget de chat
}

/**
 * Mapeo de IDs de bots a sus correspondientes configuraciones de Dialogflow
 *
 * ✅ Todos los agent IDs han sido actualizados con los valores reales de Google Cloud Dialogflow
 */
export const botAgentMap: Record<string, DialogflowConfig> = {
  'demos-bot-informacion-general': {
    projectId: 'demos-bot-informacion-general',
    agentId: '042401fe-a6b8-4976-86ea-d8cc28feb98f', // ✅ ID real configurado
    chatTitle: 'bot-demo-general'
  },
  'demos-bot-servicios': {
    projectId: 'demos-bot-informacion-general', // Usa el mismo proyecto que información general
    agentId: '042401fe-a6b8-4976-86ea-d8cc28feb98f', // ✅ ID real configurado
    chatTitle: 'bot-demo-servicios'
  },
  'demos-bot-cobranza': {
    projectId: 'demos-bot-cobranza',
    agentId: 'a4284268-1810-484c-9339-6227aea96ca0', // ✅ ID real configurado
    chatTitle: 'bot-demo-cobranza'
  },
  'demos-bot-agendamiento': {
    projectId: 'demos-bot-agendamiento',
    agentId: 'a95f3390-8d52-43e7-915d-994f8739364b', // ✅ ID real configurado
    chatTitle: 'bot-demo-agendamiento'
  }
};

// Lista de IDs de bots que deben usar Dialogflow
export const dialogflowEnabledBots = Object.keys(botAgentMap);

// Función helper para verificar si un bot tiene configuración de Dialogflow
export const hasDialogflowConfig = (botId: string): boolean => {
  return botId in botAgentMap;
};

// Función helper para obtener la configuración de Dialogflow de un bot
export const getDialogflowConfig = (botId: string): DialogflowConfig | null => {
  return botAgentMap[botId] || null;
};
