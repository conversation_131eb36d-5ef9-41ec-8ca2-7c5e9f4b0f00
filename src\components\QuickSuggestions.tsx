import React, { memo } from 'react';
import { MessageSquare, Phone, Calendar, ExternalLink, Mail } from 'lucide-react';
import { ResponseAction } from '../utils/chatResponseEngine';

interface QuickSuggestionsProps {
  suggestions: string[];
  actions?: ResponseAction[];
  onSuggestionClick: (suggestion: string) => void;
  onActionClick: (action: ResponseAction) => void;
}

const QuickSuggestions: React.FC<QuickSuggestionsProps> = ({
  suggestions,
  actions = [],
  onSuggestionClick,
  onActionClick
}) => {
  const getActionIcon = (type: string) => {
    switch (type) {
      case 'phone':
        return Phone;
      case 'email':
        return Mail;
      case 'schedule':
        return Calendar;
      case 'link':
        return ExternalLink;
      default:
        return MessageSquare;
    }
  };

  const getActionColor = (type: string) => {
    switch (type) {
      case 'phone':
        return 'bg-green-100 text-green-700 hover:bg-green-200';
      case 'email':
        return 'bg-blue-100 text-blue-700 hover:bg-blue-200';
      case 'schedule':
        return 'bg-purple-100 text-purple-700 hover:bg-purple-200';
      case 'link':
        return 'bg-indigo-100 text-indigo-700 hover:bg-indigo-200';
      default:
        return 'bg-gray-100 text-gray-700 hover:bg-gray-200';
    }
  };

  if (suggestions.length === 0 && actions.length === 0) {
    return null;
  }

  return (
    <div className="px-4 py-3 bg-gray-50/50 border-t border-gray-200/50">
      <div className="max-w-4xl mx-auto">
        {/* Sugerencias de texto */}
        {suggestions.length > 0 && (
          <div className="mb-3">
            <p className="text-xs text-gray-500 mb-2 font-medium">Sugerencias:</p>
            <div className="flex flex-wrap gap-2">
              {suggestions.map((suggestion, index) => (
                <button
                  key={index}
                  onClick={() => onSuggestionClick(suggestion)}
                  className="inline-flex items-center px-3 py-1.5 text-sm bg-white border border-gray-200 rounded-full hover:bg-gray-50 hover:border-gray-300 transition-colors duration-200 text-gray-700"
                >
                  <MessageSquare className="w-3 h-3 mr-1.5" />
                  {suggestion}
                </button>
              ))}
            </div>
          </div>
        )}

        {/* Acciones */}
        {actions.length > 0 && (
          <div>
            <p className="text-xs text-gray-500 mb-2 font-medium">Acciones rápidas:</p>
            <div className="flex flex-wrap gap-2">
              {actions.map((action, index) => {
                const Icon = getActionIcon(action.type);
                const colorClass = getActionColor(action.type);
                
                return (
                  <button
                    key={index}
                    onClick={() => onActionClick(action)}
                    className={`inline-flex items-center px-3 py-1.5 text-sm rounded-full transition-colors duration-200 font-medium ${colorClass}`}
                  >
                    <Icon className="w-3 h-3 mr-1.5" />
                    {action.label}
                  </button>
                );
              })}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default memo(QuickSuggestions);
