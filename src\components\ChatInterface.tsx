import React, { useState, useRef, useEffect } from 'react';
import { Bot, Message } from '../types/bot';
import { ArrowLeft, Send, User, Minimize2, Phone, Calendar, ExternalLink, History } from 'lucide-react';
import { ChatResponseEngine, ResponseContext, ConversationState, ResponseAction } from '../utils/chatResponseEngine';
import QuickSuggestions from './QuickSuggestions';
import EnhancedMessage from './EnhancedMessage';
import ConversationHistory from './ConversationHistory';
import { useConversationPersistence, ConversationData } from '../hooks/useConversationPersistence';

interface ChatInterfaceProps {
  bot: Bot;
  onBack: () => void;
}

const ChatInterface: React.FC<ChatInterfaceProps> = ({ bot, onBack }) => {
  const {
    messages,
    setMessages,
    isLoading: isLoadingConversation,
    saveConversation,
    getBotConversations,
    deleteConversation,
    clearAllConversations
  } = useConversationPersistence(bot.id);

  const [inputValue, setInputValue] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [showHistory, setShowHistory] = useState(false);
  const [conversationState, setConversationState] = useState<ConversationState>({
    stage: 'greeting',
    userIntent: null,
    collectedInfo: {},
    lastBotAction: null
  });
  const [currentSuggestions, setCurrentSuggestions] = useState<string[]>([
    'Ver servicios',
    'Solicitar información',
    'Hablar con un agente'
  ]);
  const [currentActions, setCurrentActions] = useState<ResponseAction[]>([]);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const responseEngine = useRef(new ChatResponseEngine());

  // Inicializar conversación si está vacía
  useEffect(() => {
    if (!isLoadingConversation && messages.length === 0) {
      const welcomeMessage: Message = {
        id: '1',
        content: `¡Hola! Soy ${bot.name}. ${bot.description}\n\n¿En qué puedo ayudarte hoy?`,
        sender: 'bot',
        timestamp: new Date()
      };
      setMessages([welcomeMessage]);
    }
  }, [isLoadingConversation, messages.length, bot.name, bot.description, setMessages]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const handleSendMessage = async (messageText?: string) => {
    const textToSend = messageText || inputValue;
    if (!textToSend.trim()) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      content: textToSend,
      sender: 'user',
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputValue('');
    setIsTyping(true);

    // Generar respuesta usando el motor de respuestas mejorado
    setTimeout(() => {
      const context: ResponseContext = {
        previousMessages: [...messages, userMessage],
        currentInput: textToSend,
        bot,
        conversationState
      };

      const enhancedResponse = responseEngine.current.generateResponse(context);

      const botResponse: Message = {
        id: (Date.now() + 1).toString(),
        content: enhancedResponse.content,
        sender: 'bot',
        timestamp: new Date()
      };

      setMessages(prev => [...prev, botResponse]);
      setIsTyping(false);

      // Actualizar sugerencias y acciones
      setCurrentSuggestions(enhancedResponse.suggestions || []);
      setCurrentActions(enhancedResponse.actions || []);

      // Actualizar estado de conversación
      if (enhancedResponse.metadata?.nextStage) {
        setConversationState(prev => ({
          ...prev,
          stage: enhancedResponse.metadata!.nextStage as any,
          userIntent: enhancedResponse.metadata!.intent,
          lastBotAction: enhancedResponse.metadata!.intent
        }));
      }
    }, 800 + Math.random() * 1200);
  };

  const handleSuggestionClick = (suggestion: string) => {
    handleSendMessage(suggestion);
  };

  const handleActionClick = (action: ResponseAction) => {
    switch (action.type) {
      case 'phone':
        if (action.value) {
          window.open(`tel:${action.value}`, '_self');
        }
        break;
      case 'email':
        if (action.value) {
          window.open(`mailto:${action.value}`, '_self');
        }
        break;
      case 'link':
        if (action.value) {
          window.open(action.value, '_blank', 'noopener,noreferrer');
        }
        break;
      case 'schedule':
        // Simular acción de agendamiento
        handleSendMessage('Quiero agendar una cita');
        break;
      default:
        console.log('Acción no manejada:', action);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  // Efecto para generar sugerencias iniciales basadas en el bot
  useEffect(() => {
    const initialContext: ResponseContext = {
      previousMessages: [],
      currentInput: '',
      bot,
      conversationState
    };

    const initialResponse = responseEngine.current.generateResponse({
      ...initialContext,
      currentInput: 'greeting'
    });

    setCurrentSuggestions(initialResponse.suggestions || []);
    setCurrentActions(initialResponse.actions || []);
  }, [bot.id]);

  return (
    <div className="h-screen flex flex-col bg-gradient-to-br from-gray-50 via-white to-blue-50/30 overflow-x-hidden">
      {/* Mobile-optimized Header */}
      <div className="bg-white/80 backdrop-blur-xl border-b border-gray-200/50 px-3 sm:px-4 lg:px-8 py-3 sm:py-4 shadow-sm safe-top sticky top-0 z-40">
        <div className="max-w-4xl mx-auto flex items-center justify-between">
          <div className="flex items-center min-w-0 flex-1">
            <button
              onClick={onBack}
              className="flex items-center text-blue-600 hover:text-blue-700 font-medium mr-3 sm:mr-6 transition-colors duration-200 group touch-target"
            >
              <ArrowLeft className="h-4 w-4 mr-1 sm:mr-2 group-hover:-translate-x-1 transition-transform" />
              <span className="hidden sm:inline">Volver</span>
            </button>
            <div className="flex items-center min-w-0 flex-1">
              <div className="relative flex-shrink-0">
                <div className="h-8 w-8 sm:h-12 sm:w-12 rounded-xl overflow-hidden bg-gray-100 shadow-sm">
                  <img
                    src={bot.imageUrl}
                    alt={bot.name}
                    className="w-full h-full object-cover"
                  />
                </div>
                <div className="absolute -bottom-0.5 -right-0.5 sm:-bottom-1 sm:-right-1 h-3 w-3 sm:h-4 sm:w-4 bg-green-500 rounded-full border-2 border-white"></div>
              </div>
              <div className="ml-2 sm:ml-4 min-w-0 flex-1">
                <h1 className="text-sm sm:text-lg font-bold text-gray-900 text-ellipsis-mobile">{bot.name}</h1>
                <p className="text-xs sm:text-sm text-green-600 font-medium">En línea</p>
              </div>
            </div>
          </div>
          <button
            onClick={onBack}
            className="flex items-center px-2 py-1.5 sm:px-4 sm:py-2 text-xs sm:text-sm font-medium text-gray-700 bg-gray-100/80 backdrop-blur-sm rounded-xl hover:bg-gray-200/80 transition-all duration-200 touch-target"
          >
            <Minimize2 className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2" />
            <span className="hidden sm:inline">Finalizar Demo</span>
            <span className="sm:hidden">Salir</span>
          </button>
        </div>
      </div>

      {/* Messages */}
      <div className="flex-1 overflow-y-auto scroll-smooth-mobile">
        <div className="max-w-4xl mx-auto px-3 sm:px-4 lg:px-8 py-4 sm:py-8">
          <div className="space-y-6">
            {messages.map((message) => (
              <EnhancedMessage
                key={message.id}
                message={message}
                bot={bot}
                showMetadata={false}
              />
            ))}

            {isTyping && (
              <EnhancedMessage
                message={{
                  id: 'typing',
                  content: '',
                  sender: 'bot',
                  timestamp: new Date()
                }}
                bot={bot}
                isTyping={true}
              />
            )}
            <div ref={messagesEndRef} />
          </div>
        </div>
      </div>

      {/* Quick Suggestions */}
      {(currentSuggestions.length > 0 || currentActions.length > 0) && !isTyping && (
        <QuickSuggestions
          suggestions={currentSuggestions}
          actions={currentActions}
          onSuggestionClick={handleSuggestionClick}
          onActionClick={handleActionClick}
        />
      )}

      {/* Mobile-optimized Input */}
      <div className="bg-white/80 backdrop-blur-xl border-t border-gray-200/50 shadow-lg safe-bottom">
        <div className="max-w-4xl mx-auto px-3 sm:px-4 lg:px-8 py-3 sm:py-4">
          <div className="flex space-x-2 sm:space-x-4">
            <div className="flex-1">
              <textarea
                value={inputValue}
                onChange={(e) => setInputValue(e.target.value)}
                onKeyPress={handleKeyPress}
                placeholder="Escribe tu mensaje..."
                className="w-full resize-none border border-gray-300/50 rounded-xl px-3 py-2.5 sm:px-4 sm:py-3 bg-white/50 backdrop-blur-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 text-sm sm:text-base"
                rows={1}
                style={{ minHeight: '44px', maxHeight: '120px' }}
              />
            </div>
            <button
              onClick={() => handleSendMessage()}
              disabled={!inputValue.trim() || isTyping}
              className="px-3 py-2.5 sm:px-6 sm:py-3 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-xl hover:from-blue-700 hover:to-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-sm hover:shadow-md touch-target"
            >
              <Send className="h-4 w-4 sm:h-5 sm:w-5" />
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ChatInterface;