import { useCallback } from 'react';

/**
 * Hook para manejar el reset completo de la aplicación
 * Simula un refresh (F5) pero de manera controlada
 */
export const useAppReset = () => {
  
  const resetDialogflowState = useCallback(() => {
    // Limpiar todos los widgets de Dialogflow del DOM
    const existingWidgets = document.querySelectorAll('df-messenger');
    existingWidgets.forEach(widget => {
      try {
        if (widget.parentNode) {
          widget.parentNode.removeChild(widget);
        }
      } catch (e) {
        console.warn('Error removing Dialogflow widget:', e);
      }
    });

    // Limpiar scripts de Dialogflow
    const scripts = document.querySelectorAll('script[src*="df-messenger.js"]');
    scripts.forEach(script => {
      try {
        if (script.parentNode) {
          script.parentNode.removeChild(script);
        }
      } catch (e) {
        console.warn('Error removing Dialogflow script:', e);
      }
    });

    // Limpiar estilos de Dialogflow
    const styles = document.querySelectorAll('style');
    styles.forEach(style => {
      if (style.textContent?.includes('df-messenger') || 
          style.textContent?.includes('--df-messenger-')) {
        try {
          if (style.parentNode) {
            style.parentNode.removeChild(style);
          }
        } catch (e) {
          console.warn('Error removing Dialogflow style:', e);
        }
      }
    });

    // Limpiar CSS links de Dialogflow
    const cssLinks = document.querySelectorAll('link[href*="df-messenger"]');
    cssLinks.forEach(link => {
      try {
        if (link.parentNode) {
          link.parentNode.removeChild(link);
        }
      } catch (e) {
        console.warn('Error removing Dialogflow CSS link:', e);
      }
    });

    console.log('[AppReset] Dialogflow state completely reset');
  }, []);

  const performHardRefresh = useCallback(() => {
    console.log('[AppReset] Performing hard refresh...');
    
    // Limpiar estado de Dialogflow
    resetDialogflowState();
    
    // Limpiar localStorage relacionado con Dialogflow si existe
    try {
      const keys = Object.keys(localStorage);
      keys.forEach(key => {
        if (key.includes('dialogflow') || key.includes('df-messenger')) {
          localStorage.removeItem(key);
        }
      });
    } catch (e) {
      console.warn('Error clearing Dialogflow localStorage:', e);
    }

    // Limpiar sessionStorage relacionado con Dialogflow si existe
    try {
      const keys = Object.keys(sessionStorage);
      keys.forEach(key => {
        if (key.includes('dialogflow') || key.includes('df-messenger')) {
          sessionStorage.removeItem(key);
        }
      });
    } catch (e) {
      console.warn('Error clearing Dialogflow sessionStorage:', e);
    }

    console.log('[AppReset] Hard refresh completed');
  }, [resetDialogflowState]);

  const forcePageReload = useCallback(() => {
    console.log('[AppReset] Forcing page reload...');
    window.location.reload();
  }, []);

  return {
    resetDialogflowState,
    performHardRefresh,
    forcePageReload
  };
};
