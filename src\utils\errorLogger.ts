/**
 * Sistema de logging centralizado para errores de la aplicación
 * Permite un mejor seguimiento y debugging de problemas
 */

export type LogLevel = 'error' | 'warn' | 'info' | 'debug';

export interface ErrorLogEntry {
  timestamp: Date;
  level: LogLevel;
  component: string;
  message: string;
  error?: any;
  context?: Record<string, any>;
  userAgent?: string;
  url?: string;
}

class ErrorLogger {
  private logs: ErrorLogEntry[] = [];
  private maxLogs = 100; // Mantener solo los últimos 100 logs

  /**
   * Registra un error en el sistema de logging
   */
  log(level: LogLevel, component: string, message: string, error?: any, context?: Record<string, any>) {
    const entry: ErrorLogEntry = {
      timestamp: new Date(),
      level,
      component,
      message,
      error,
      context,
      userAgent: navigator.userAgent,
      url: window.location.href
    };

    this.logs.push(entry);

    // Mantener solo los logs más recientes
    if (this.logs.length > this.maxLogs) {
      this.logs = this.logs.slice(-this.maxLogs);
    }

    // Log en consola según el nivel
    this.logToConsole(entry);

    // En producción, aquí se podría enviar a un servicio de logging externo
    if (process.env.NODE_ENV === 'production' && level === 'error') {
      this.sendToExternalService(entry);
    }
  }

  /**
   * Métodos de conveniencia para diferentes niveles de log
   */
  error(component: string, message: string, error?: any, context?: Record<string, any>) {
    this.log('error', component, message, error, context);
  }

  warn(component: string, message: string, context?: Record<string, any>) {
    this.log('warn', component, message, undefined, context);
  }

  info(component: string, message: string, context?: Record<string, any>) {
    this.log('info', component, message, undefined, context);
  }

  debug(component: string, message: string, context?: Record<string, any>) {
    this.log('debug', component, message, undefined, context);
  }

  /**
   * Obtiene todos los logs registrados
   */
  getLogs(): ErrorLogEntry[] {
    return [...this.logs];
  }

  /**
   * Obtiene logs filtrados por componente
   */
  getLogsByComponent(component: string): ErrorLogEntry[] {
    return this.logs.filter(log => log.component === component);
  }

  /**
   * Obtiene logs filtrados por nivel
   */
  getLogsByLevel(level: LogLevel): ErrorLogEntry[] {
    return this.logs.filter(log => log.level === level);
  }

  /**
   * Limpia todos los logs
   */
  clearLogs() {
    this.logs = [];
  }

  /**
   * Exporta logs como JSON para debugging
   */
  exportLogs(): string {
    return JSON.stringify(this.logs, null, 2);
  }

  private logToConsole(entry: ErrorLogEntry) {
    const prefix = `[${entry.timestamp.toISOString()}] [${entry.component}]`;
    
    switch (entry.level) {
      case 'error':
        console.error(prefix, entry.message, entry.error, entry.context);
        break;
      case 'warn':
        console.warn(prefix, entry.message, entry.context);
        break;
      case 'info':
        console.info(prefix, entry.message, entry.context);
        break;
      case 'debug':
        console.debug(prefix, entry.message, entry.context);
        break;
    }
  }

  private async sendToExternalService(entry: ErrorLogEntry) {
    // En un entorno real, aquí se enviarían los errores a un servicio como Sentry, LogRocket, etc.
    try {
      // Ejemplo de implementación:
      // await fetch('/api/errors', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify(entry)
      // });
      
      console.info('Error logged for external service:', entry);
    } catch (error) {
      console.warn('Failed to send error to external service:', error);
    }
  }
}

// Instancia singleton del logger
export const errorLogger = new ErrorLogger();

/**
 * Hook para usar el logger en componentes React
 */
export const useErrorLogger = () => {
  return {
    logError: (component: string, message: string, error?: any, context?: Record<string, any>) => 
      errorLogger.error(component, message, error, context),
    logWarn: (component: string, message: string, context?: Record<string, any>) => 
      errorLogger.warn(component, message, context),
    logInfo: (component: string, message: string, context?: Record<string, any>) => 
      errorLogger.info(component, message, context),
    logDebug: (component: string, message: string, context?: Record<string, any>) => 
      errorLogger.debug(component, message, context),
    getLogs: () => errorLogger.getLogs(),
    exportLogs: () => errorLogger.exportLogs()
  };
};
