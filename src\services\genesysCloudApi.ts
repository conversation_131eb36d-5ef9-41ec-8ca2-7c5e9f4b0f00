// Configuración de la API de Genesys Cloud
const GENESYS_API_BASE_URL = 'https://api.mypurecloud.com/api/v2';
const GENESYS_AUTH_URL = 'https://login.mypurecloud.com/oauth/token?grant_type=client_credentials';
const CONTACT_LIST_ID = '6b13023c-b8fa-4012-91cf-69b427275448';
const CLIENT_CREDENTIALS = 'NzIwM2VjZmUtOGYwMC00ZmU3LThhNjQtMjY2NDRjMGY1NTdmOjhWa2FPS1FfZmtNMWNsT2I3YlpUaFNEUFJSb3c1WVE5aXhqTzE3Q0NOVW8=';

// Variables para manejo de token
let currentToken: string | null = null;
let tokenExpirationTime: number | null = null;

export interface ContactData {
  dni: string;
  nombre: string;
  apellido: string;
  phone1: string;
  fec_vencimiento: string;
  fec_plazo: string;
  monto_deuda: string;
  propuesta_deuda: string;
  correo_electronico: string;
}

export interface TokenResponse {
  access_token: string;
  token_type: string;
  expires_in: number;
}

export interface GenesysContact {
  id: string;
  contactListId: string;
  data: {
    dni: string;
    NOMBRE: string;
    APELLIDO: string;
    PHONE1: string;
    FEC_VENCIMIENTO: string;
    FEC_PLAZO: string;
    MONTO_DEUDA: string;
    PROPUESTA_DEUDA: string;
    CORREO_ELECTRONICO: string;
  };
  callable: boolean;
  phoneNumberStatus: Record<string, any>;
  contactableStatus: Record<string, any>;
}

// Función para obtener un nuevo token de acceso
const getAccessToken = async (): Promise<string> => {
  try {
    console.log('Solicitando nuevo token de acceso...');

    const response = await fetch(GENESYS_AUTH_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Authorization': `Basic ${CLIENT_CREDENTIALS}`
      }
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Error al obtener token:', {
        status: response.status,
        statusText: response.statusText,
        body: errorText
      });
      throw new Error(`Error ${response.status}: ${response.statusText}`);
    }

    const tokenData: TokenResponse = await response.json();
    console.log('Token obtenido exitosamente, expira en:', tokenData.expires_in, 'segundos');

    // Guardar token y tiempo de expiración (con un margen de 5 minutos)
    currentToken = tokenData.access_token;
    tokenExpirationTime = Date.now() + (tokenData.expires_in - 300) * 1000; // 5 minutos antes de expirar

    return tokenData.access_token;
  } catch (error) {
    console.error('Error al obtener token de acceso:', error);
    throw new Error('No se pudo obtener el token de acceso');
  }
};

// Función para verificar si el token actual es válido
const isTokenValid = (): boolean => {
  return currentToken !== null &&
         tokenExpirationTime !== null &&
         Date.now() < tokenExpirationTime;
};

// Función para obtener un token válido (reutiliza el actual o obtiene uno nuevo)
const getValidToken = async (): Promise<string> => {
  if (isTokenValid()) {
    console.log('Usando token existente');
    return currentToken!;
  }

  console.log('Token expirado o no existe, obteniendo nuevo token...');
  return await getAccessToken();
};

// Función para formatear fecha de YYYY-MM-DD a DD/MM/YYYY
const formatDateForGenesys = (dateString: string): string => {
  const date = new Date(dateString);
  const day = date.getDate().toString().padStart(2, '0');
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const year = date.getFullYear();
  return `${day}/${month}/${year}`;
};

// Función para transformar los datos del formulario al formato de Genesys
const transformContactData = (contactData: ContactData): GenesysContact => {
  return {
    id: "",
    contactListId: CONTACT_LIST_ID,
    data: {
      dni: contactData.dni,
      NOMBRE: contactData.nombre,
      APELLIDO: contactData.apellido,
      PHONE1: contactData.phone1,
      FEC_VENCIMIENTO: formatDateForGenesys(contactData.fec_vencimiento),
      FEC_PLAZO: formatDateForGenesys(contactData.fec_plazo),
      MONTO_DEUDA: contactData.monto_deuda,
      PROPUESTA_DEUDA: contactData.propuesta_deuda,
      CORREO_ELECTRONICO: contactData.correo_electronico
    },
    callable: true,
    phoneNumberStatus: {},
    contactableStatus: {}
  };
};

// Función principal para crear un contacto en Genesys Cloud
export const createContact = async (contactData: ContactData): Promise<{ success: boolean; message: string; data?: any }> => {
  try {
    // Obtener un token válido
    const token = await getValidToken();

    const genesysContact = transformContactData(contactData);
    const url = `${GENESYS_API_BASE_URL}/outbound/contactlists/${CONTACT_LIST_ID}/contacts`;

    console.log('Enviando contacto a Genesys Cloud:', genesysContact);

    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify([genesysContact])
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Error en la respuesta de Genesys Cloud:', {
        status: response.status,
        statusText: response.statusText,
        body: errorText
      });

      // Si es error 401, intentar renovar token y reintentar una vez
      if (response.status === 401) {
        console.log('Token expirado, obteniendo nuevo token y reintentando...');
        currentToken = null; // Forzar renovación
        const newToken = await getValidToken();

        const retryResponse = await fetch(url, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${newToken}`
          },
          body: JSON.stringify([genesysContact])
        });

        if (!retryResponse.ok) {
          const retryErrorText = await retryResponse.text();
          console.error('Error en segundo intento:', {
            status: retryResponse.status,
            statusText: retryResponse.statusText,
            body: retryErrorText
          });
          throw new Error(`Error ${retryResponse.status}: ${retryResponse.statusText}`);
        }

        const retryResponseData = await retryResponse.json();
        console.log('Contacto creado exitosamente en segundo intento:', retryResponseData);

        return {
          success: true,
          message: 'Contacto creado exitosamente en Genesys Cloud',
          data: retryResponseData
        };
      }

      throw new Error(`Error ${response.status}: ${response.statusText}`);
    }

    const responseData = await response.json();
    console.log('Contacto creado exitosamente:', responseData);

    return {
      success: true,
      message: 'Contacto creado exitosamente en Genesys Cloud',
      data: responseData
    };

  } catch (error) {
    console.error('Error al crear contacto en Genesys Cloud:', error);

    let errorMessage = 'Error desconocido al crear el contacto';

    if (error instanceof Error) {
      errorMessage = error.message;
    }

    // Manejar errores específicos
    if (errorMessage.includes('401')) {
      errorMessage = 'Error de autenticación. No se pudo obtener acceso válido.';
    } else if (errorMessage.includes('403')) {
      errorMessage = 'Sin permisos para crear contactos en esta lista.';
    } else if (errorMessage.includes('404')) {
      errorMessage = 'Lista de contactos no encontrada.';
    } else if (errorMessage.includes('400')) {
      errorMessage = 'Datos del contacto inválidos.';
    } else if (errorMessage.includes('NetworkError') || errorMessage.includes('fetch')) {
      errorMessage = 'Error de conexión. Verifica tu conexión a internet.';
    } else if (errorMessage.includes('No se pudo obtener el token')) {
      errorMessage = 'Error de autenticación. No se pudo obtener token de acceso.';
    }

    return {
      success: false,
      message: errorMessage
    };
  }
};

// Función para validar el token actual
export const validateCurrentToken = async (): Promise<boolean> => {
  try {
    if (!currentToken) {
      return false;
    }

    const response = await fetch(`${GENESYS_API_BASE_URL}/users/me`, {
      headers: {
        'Authorization': `Bearer ${currentToken}`
      }
    });

    return response.ok;
  } catch (error) {
    console.error('Error validating token:', error);
    return false;
  }
};

// Función para obtener información del token actual
export const getTokenInfo = () => {
  return {
    hasToken: currentToken !== null,
    isValid: isTokenValid(),
    expiresAt: tokenExpirationTime ? new Date(tokenExpirationTime) : null,
    timeUntilExpiration: tokenExpirationTime ? Math.max(0, tokenExpirationTime - Date.now()) : 0
  };
};

// Función para forzar renovación del token
export const refreshToken = async (): Promise<boolean> => {
  try {
    currentToken = null;
    tokenExpirationTime = null;
    await getValidToken();
    return true;
  } catch (error) {
    console.error('Error refreshing token:', error);
    return false;
  }
};
