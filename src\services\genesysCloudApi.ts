// Configuración de la API de Genesys Cloud
const GENESYS_API_BASE_URL = 'https://api.mypurecloud.com/api/v2';
const CONTACT_LIST_ID = '6b13023c-b8fa-4012-91cf-69b427275448';
const BEARER_TOKEN = 'a6bRZeUFUH-jWNuF5dbzNRwdaJmFU5a3yE5_LR7kfoM4T3ghs5aAV3kAEHVVL24Xo_pRTHgOY59ZCo7xB27OZA';

export interface ContactData {
  dni: string;
  nombre: string;
  apellido: string;
  phone1: string;
  fec_vencimiento: string;
  fec_plazo: string;
  monto_deuda: string;
  propuesta_deuda: string;
  correo_electronico: string;
}

export interface GenesysContact {
  id: string;
  contactListId: string;
  data: {
    dni: string;
    NOMBRE: string;
    APELLIDO: string;
    PHONE1: string;
    FEC_VENCIMIENTO: string;
    FEC_PLAZO: string;
    MONTO_DEUDA: string;
    PROPUESTA_DEUDA: string;
    CORREO_ELECTRONICO: string;
  };
  callable: boolean;
  phoneNumberStatus: Record<string, any>;
  contactableStatus: Record<string, any>;
}

// Función para formatear fecha de YYYY-MM-DD a DD/MM/YYYY
const formatDateForGenesys = (dateString: string): string => {
  const date = new Date(dateString);
  const day = date.getDate().toString().padStart(2, '0');
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const year = date.getFullYear();
  return `${day}/${month}/${year}`;
};

// Función para transformar los datos del formulario al formato de Genesys
const transformContactData = (contactData: ContactData): GenesysContact => {
  return {
    id: "",
    contactListId: CONTACT_LIST_ID,
    data: {
      dni: contactData.dni,
      NOMBRE: contactData.nombre,
      APELLIDO: contactData.apellido,
      PHONE1: contactData.phone1,
      FEC_VENCIMIENTO: formatDateForGenesys(contactData.fec_vencimiento),
      FEC_PLAZO: formatDateForGenesys(contactData.fec_plazo),
      MONTO_DEUDA: contactData.monto_deuda,
      PROPUESTA_DEUDA: contactData.propuesta_deuda,
      CORREO_ELECTRONICO: contactData.correo_electronico
    },
    callable: true,
    phoneNumberStatus: {},
    contactableStatus: {}
  };
};

// Función principal para crear un contacto en Genesys Cloud
export const createContact = async (contactData: ContactData): Promise<{ success: boolean; message: string; data?: any }> => {
  try {
    const genesysContact = transformContactData(contactData);
    const url = `${GENESYS_API_BASE_URL}/outbound/contactlists/${CONTACT_LIST_ID}/contacts`;
    
    console.log('Enviando contacto a Genesys Cloud:', genesysContact);
    
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${BEARER_TOKEN}`
      },
      body: JSON.stringify([genesysContact])
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Error en la respuesta de Genesys Cloud:', {
        status: response.status,
        statusText: response.statusText,
        body: errorText
      });
      
      throw new Error(`Error ${response.status}: ${response.statusText}`);
    }

    const responseData = await response.json();
    console.log('Contacto creado exitosamente:', responseData);
    
    return {
      success: true,
      message: 'Contacto creado exitosamente en Genesys Cloud',
      data: responseData
    };
    
  } catch (error) {
    console.error('Error al crear contacto en Genesys Cloud:', error);
    
    let errorMessage = 'Error desconocido al crear el contacto';
    
    if (error instanceof Error) {
      errorMessage = error.message;
    }
    
    // Manejar errores específicos
    if (errorMessage.includes('401')) {
      errorMessage = 'Error de autenticación. Token inválido o expirado.';
    } else if (errorMessage.includes('403')) {
      errorMessage = 'Sin permisos para crear contactos en esta lista.';
    } else if (errorMessage.includes('404')) {
      errorMessage = 'Lista de contactos no encontrada.';
    } else if (errorMessage.includes('400')) {
      errorMessage = 'Datos del contacto inválidos.';
    } else if (errorMessage.includes('NetworkError') || errorMessage.includes('fetch')) {
      errorMessage = 'Error de conexión. Verifica tu conexión a internet.';
    }
    
    return {
      success: false,
      message: errorMessage
    };
  }
};

// Función para validar el token (opcional)
export const validateToken = async (): Promise<boolean> => {
  try {
    const response = await fetch(`${GENESYS_API_BASE_URL}/users/me`, {
      headers: {
        'Authorization': `Bearer ${BEARER_TOKEN}`
      }
    });
    
    return response.ok;
  } catch (error) {
    console.error('Error validating token:', error);
    return false;
  }
};
