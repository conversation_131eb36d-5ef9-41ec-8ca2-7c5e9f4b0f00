import React, { useState, memo } from 'react';
import { 
  History, 
  MessageCircle, 
  Trash2, 
  Calendar,
  Clock,
  ChevronDown,
  ChevronUp,
  Download,
  Upload
} from 'lucide-react';
import { ConversationData } from '../hooks/useConversationPersistence';
import { Bot } from '../types/bot';

interface ConversationHistoryProps {
  bot: Bot;
  conversations: ConversationData[];
  onLoadConversation: (conversation: ConversationData) => void;
  onDeleteConversation: (sessionId: string) => void;
  onClearAll: () => void;
  onExportConversations?: () => void;
  onImportConversations?: (file: File) => void;
}

const ConversationHistory: React.FC<ConversationHistoryProps> = ({
  bot,
  conversations,
  onLoadConversation,
  onDeleteConversation,
  onClearAll,
  onExportConversations,
  onImportConversations
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState<string | null>(null);

  const formatDate = (date: Date) => {
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
    
    if (diffDays === 0) {
      return `Hoy ${date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}`;
    } else if (diffDays === 1) {
      return `Ayer ${date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}`;
    } else if (diffDays < 7) {
      return `Hace ${diffDays} días`;
    } else {
      return date.toLocaleDateString();
    }
  };

  const getConversationPreview = (conversation: ConversationData) => {
    const lastMessage = conversation.messages[conversation.messages.length - 1];
    if (!lastMessage) return 'Conversación vacía';
    
    const preview = lastMessage.content.substring(0, 50);
    return preview.length < lastMessage.content.length ? `${preview}...` : preview;
  };

  const handleFileImport = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file && onImportConversations) {
      onImportConversations(file);
    }
    // Reset input
    event.target.value = '';
  };

  if (conversations.length === 0) {
    return (
      <div className="bg-gray-50 rounded-lg p-4 text-center">
        <History className="h-8 w-8 text-gray-400 mx-auto mb-2" />
        <p className="text-sm text-gray-600">No hay conversaciones guardadas</p>
        <p className="text-xs text-gray-500 mt-1">
          Las conversaciones se guardan automáticamente mientras chateas
        </p>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg border border-gray-200 shadow-sm">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200">
        <div className="flex items-center">
          <History className="h-5 w-5 text-gray-500 mr-2" />
          <h3 className="font-medium text-gray-900">
            Historial de Conversaciones
          </h3>
          <span className="ml-2 bg-gray-100 text-gray-600 text-xs px-2 py-1 rounded-full">
            {conversations.length}
          </span>
        </div>
        
        <div className="flex items-center space-x-2">
          {/* Export/Import buttons */}
          {onExportConversations && (
            <button
              onClick={onExportConversations}
              className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
              title="Exportar conversaciones"
            >
              <Download className="h-4 w-4" />
            </button>
          )}
          
          {onImportConversations && (
            <label className="p-2 text-gray-400 hover:text-gray-600 transition-colors cursor-pointer" title="Importar conversaciones">
              <Upload className="h-4 w-4" />
              <input
                type="file"
                accept=".json"
                onChange={handleFileImport}
                className="hidden"
              />
            </label>
          )}
          
          <button
            onClick={() => setIsExpanded(!isExpanded)}
            className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
          >
            {isExpanded ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
          </button>
        </div>
      </div>

      {/* Conversation List */}
      {isExpanded && (
        <div className="max-h-64 overflow-y-auto">
          {conversations.map((conversation) => (
            <div
              key={conversation.sessionId}
              className="p-4 border-b border-gray-100 last:border-b-0 hover:bg-gray-50 transition-colors"
            >
              <div className="flex items-start justify-between">
                <div 
                  className="flex-1 cursor-pointer"
                  onClick={() => onLoadConversation(conversation)}
                >
                  <div className="flex items-center mb-1">
                    <MessageCircle className="h-4 w-4 text-blue-500 mr-2" />
                    <span className="text-sm font-medium text-gray-900">
                      {conversation.messages.length} mensajes
                    </span>
                  </div>
                  
                  <p className="text-sm text-gray-600 mb-2">
                    {getConversationPreview(conversation)}
                  </p>
                  
                  <div className="flex items-center text-xs text-gray-500">
                    <Calendar className="h-3 w-3 mr-1" />
                    {formatDate(conversation.lastUpdated)}
                  </div>
                </div>
                
                <div className="ml-4 flex items-center space-x-2">
                  {showDeleteConfirm === conversation.sessionId ? (
                    <div className="flex items-center space-x-1">
                      <button
                        onClick={() => {
                          onDeleteConversation(conversation.sessionId);
                          setShowDeleteConfirm(null);
                        }}
                        className="px-2 py-1 bg-red-600 text-white text-xs rounded hover:bg-red-700 transition-colors"
                      >
                        Confirmar
                      </button>
                      <button
                        onClick={() => setShowDeleteConfirm(null)}
                        className="px-2 py-1 bg-gray-300 text-gray-700 text-xs rounded hover:bg-gray-400 transition-colors"
                      >
                        Cancelar
                      </button>
                    </div>
                  ) : (
                    <button
                      onClick={() => setShowDeleteConfirm(conversation.sessionId)}
                      className="p-1 text-gray-400 hover:text-red-600 transition-colors"
                      title="Eliminar conversación"
                    >
                      <Trash2 className="h-4 w-4" />
                    </button>
                  )}
                </div>
              </div>
            </div>
          ))}
          
          {/* Clear All Button */}
          {conversations.length > 1 && (
            <div className="p-4 border-t border-gray-200 bg-gray-50">
              <button
                onClick={onClearAll}
                className="w-full flex items-center justify-center px-4 py-2 text-sm text-red-600 hover:text-red-800 hover:bg-red-50 rounded-lg transition-colors"
              >
                <Trash2 className="h-4 w-4 mr-2" />
                Eliminar todas las conversaciones
              </button>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default memo(ConversationHistory);
