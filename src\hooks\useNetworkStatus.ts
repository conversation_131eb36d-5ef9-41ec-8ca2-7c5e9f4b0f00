import { useState, useEffect } from 'react';

export interface NetworkStatus {
  isOnline: boolean;
  isSlowConnection: boolean;
  connectionType: string;
}

/**
 * Hook para monitorear el estado de la conexión de red
 * Útil para mostrar mensajes de error apropiados cuando hay problemas de conectividad
 */
export const useNetworkStatus = (): NetworkStatus => {
  const [networkStatus, setNetworkStatus] = useState<NetworkStatus>({
    isOnline: navigator.onLine,
    isSlowConnection: false,
    connectionType: 'unknown'
  });

  useEffect(() => {
    const updateNetworkStatus = () => {
      const connection = (navigator as any).connection || (navigator as any).mozConnection || (navigator as any).webkitConnection;
      
      setNetworkStatus({
        isOnline: navigator.onLine,
        isSlowConnection: connection ? connection.effectiveType === 'slow-2g' || connection.effectiveType === '2g' : false,
        connectionType: connection ? connection.effectiveType || 'unknown' : 'unknown'
      });
    };

    const handleOnline = () => {
      updateNetworkStatus();
    };

    const handleOffline = () => {
      setNetworkStatus(prev => ({
        ...prev,
        isOnline: false
      }));
    };

    // Listeners para cambios de conectividad
    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    // Listener para cambios en el tipo de conexión (si está disponible)
    const connection = (navigator as any).connection || (navigator as any).mozConnection || (navigator as any).webkitConnection;
    if (connection) {
      connection.addEventListener('change', updateNetworkStatus);
    }

    // Actualizar estado inicial
    updateNetworkStatus();

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
      if (connection) {
        connection.removeEventListener('change', updateNetworkStatus);
      }
    };
  }, []);

  return networkStatus;
};

/**
 * Hook para detectar si hay problemas de conectividad que podrían afectar la carga de recursos externos
 */
export const useConnectionHealth = () => {
  const networkStatus = useNetworkStatus();
  
  const isHealthy = networkStatus.isOnline && !networkStatus.isSlowConnection;
  const hasIssues = !networkStatus.isOnline || networkStatus.isSlowConnection;
  
  const getConnectionMessage = (): string => {
    if (!networkStatus.isOnline) {
      return 'Sin conexión a internet';
    }
    if (networkStatus.isSlowConnection) {
      return 'Conexión lenta detectada';
    }
    return 'Conexión estable';
  };

  return {
    isHealthy,
    hasIssues,
    networkStatus,
    connectionMessage: getConnectionMessage()
  };
};
